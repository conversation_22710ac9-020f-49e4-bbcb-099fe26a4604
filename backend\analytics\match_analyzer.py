"""
محلل المباراة - حساب الإحصائيات والتحليلات التفصيلية
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict, Counter
from dataclasses import dataclass
import json
from pathlib import Path

from ..ai_models.event_detection.event_classifier import EventType
from ..ai_models.pose_estimation.mediapipe_pose import PoseAction


@dataclass
class PlayerStats:
    """إحصائيات لاعب واحد"""
    player_id: int
    team_id: Optional[int] = None
    
    # إحصائيات الحركة
    total_distance: float = 0.0
    max_speed: float = 0.0
    average_speed: float = 0.0
    sprint_count: int = 0
    
    # إحصائيات الكرة
    ball_touches: int = 0
    passes_attempted: int = 0
    passes_completed: int = 0
    shots: int = 0
    goals: int = 0
    
    # إحصائيات دفاعية
    tackles: int = 0
    interceptions: int = 0
    clearances: int = 0
    
    # إحصائيات أخرى
    fouls_committed: int = 0
    fouls_received: int = 0
    yellow_cards: int = 0
    red_cards: int = 0
    
    # تقييمات الأداء
    technical_rating: float = 0.0
    physical_rating: float = 0.0
    tactical_rating: float = 0.0
    mental_rating: float = 0.0
    overall_rating: float = 0.0
    
    @property
    def pass_accuracy(self) -> float:
        """دقة التمرير"""
        if self.passes_attempted == 0:
            return 0.0
        return (self.passes_completed / self.passes_attempted) * 100


@dataclass
class TeamStats:
    """إحصائيات فريق"""
    team_id: int
    team_name: str = ""
    
    # إحصائيات عامة
    possession_percentage: float = 0.0
    total_passes: int = 0
    pass_accuracy: float = 0.0
    shots: int = 0
    shots_on_target: int = 0
    goals: int = 0
    
    # إحصائيات دفاعية
    tackles: int = 0
    interceptions: int = 0
    clearances: int = 0
    
    # إحصائيات انضباطية
    fouls: int = 0
    yellow_cards: int = 0
    red_cards: int = 0
    
    # إحصائيات تكتيكية
    formation: str = ""
    average_position: Dict[int, Tuple[float, float]] = None
    
    def __post_init__(self):
        if self.average_position is None:
            self.average_position = {}


@dataclass
class MatchStats:
    """إحصائيات المباراة الكاملة"""
    match_id: str
    duration: float  # بالثواني
    
    # إحصائيات الفرق
    team_stats: Dict[int, TeamStats]
    
    # إحصائيات اللاعبين
    player_stats: Dict[int, PlayerStats]
    
    # أحداث المباراة
    events: List[Dict[str, Any]]
    
    # خرائط حرارية
    heatmaps: Dict[int, np.ndarray]  # player_id -> heatmap
    
    # إحصائيات عامة
    total_events: int = 0
    ball_in_play_time: float = 0.0
    
    def __post_init__(self):
        if not hasattr(self, 'team_stats') or self.team_stats is None:
            self.team_stats = {}
        if not hasattr(self, 'player_stats') or self.player_stats is None:
            self.player_stats = {}
        if not hasattr(self, 'events') or self.events is None:
            self.events = []
        if not hasattr(self, 'heatmaps') or self.heatmaps is None:
            self.heatmaps = {}


class MatchAnalyzer:
    """محلل المباراة"""
    
    def __init__(self, field_dimensions: Tuple[float, float] = (105.0, 68.0)):
        """
        field_dimensions: أبعاد الملعب بالمتر (طول، عرض)
        """
        self.field_length, self.field_width = field_dimensions
        self.pixel_to_meter_ratio = None
        
    def analyze_match(self, frame_data: List[Dict[str, Any]], 
                     video_fps: float = 30.0,
                     frame_dimensions: Tuple[int, int] = (1920, 1080)) -> MatchStats:
        """تحليل مباراة كاملة"""
        
        # تقدير نسبة البكسل إلى المتر
        self.pixel_to_meter_ratio = self._estimate_pixel_to_meter_ratio(frame_dimensions)
        
        # إنشاء إحصائيات المباراة
        match_stats = MatchStats(
            match_id=f"match_{int(time.time())}",
            duration=len(frame_data) / video_fps,
            team_stats={},
            player_stats={},
            events=[],
            heatmaps={}
        )
        
        # تحليل البيانات
        self._analyze_player_movements(frame_data, match_stats, video_fps)
        self._analyze_events(frame_data, match_stats)
        self._calculate_team_stats(match_stats)
        self._generate_heatmaps(frame_data, match_stats, frame_dimensions)
        self._calculate_ratings(match_stats)
        
        return match_stats
    
    def _estimate_pixel_to_meter_ratio(self, frame_dimensions: Tuple[int, int]) -> float:
        """تقدير نسبة البكسل إلى المتر"""
        # تقدير بسيط - يمكن تحسينه بكشف خطوط الملعب
        frame_width = frame_dimensions[0]
        estimated_ratio = self.field_length / frame_width
        return estimated_ratio
    
    def _analyze_player_movements(self, frame_data: List[Dict[str, Any]], 
                                 match_stats: MatchStats, fps: float):
        """تحليل حركات اللاعبين"""
        player_positions = defaultdict(list)  # player_id -> [(x, y, timestamp), ...]
        
        # جمع مواقع اللاعبين
        for frame in frame_data:
            timestamp = frame["timestamp"]
            
            for track_id_str, track_data in frame["tracks"].items():
                track_id = int(track_id_str)
                if track_data["class_name"] in ["player", "goalkeeper"]:
                    center_x = (track_data["x1"] + track_data["x2"]) / 2
                    center_y = (track_data["y1"] + track_data["y2"]) / 2
                    player_positions[track_id].append((center_x, center_y, timestamp))
        
        # حساب إحصائيات الحركة لكل لاعب
        for player_id, positions in player_positions.items():
            if len(positions) < 2:
                continue
                
            player_stats = PlayerStats(player_id=player_id)
            
            # حساب المسافة الإجمالية والسرعة
            total_distance = 0.0
            speeds = []
            
            for i in range(1, len(positions)):
                prev_pos = positions[i-1]
                curr_pos = positions[i]
                
                # المسافة بالبكسل
                pixel_distance = np.sqrt(
                    (curr_pos[0] - prev_pos[0])**2 + 
                    (curr_pos[1] - prev_pos[1])**2
                )
                
                # تحويل إلى متر
                meter_distance = pixel_distance * self.pixel_to_meter_ratio
                total_distance += meter_distance
                
                # حساب السرعة (م/ث)
                time_diff = curr_pos[2] - prev_pos[2]
                if time_diff > 0:
                    speed = meter_distance / time_diff
                    speeds.append(speed)
                    
                    # عد الجري السريع (أكثر من 5 م/ث)
                    if speed > 5.0:
                        player_stats.sprint_count += 1
            
            player_stats.total_distance = total_distance
            if speeds:
                player_stats.max_speed = max(speeds)
                player_stats.average_speed = np.mean(speeds)
            
            match_stats.player_stats[player_id] = player_stats
    
    def _analyze_events(self, frame_data: List[Dict[str, Any]], match_stats: MatchStats):
        """تحليل أحداث المباراة"""
        for frame in frame_data:
            for event_data in frame["events"]:
                event_type = event_data["event_type"]
                participants = event_data["participants"]
                
                # إضافة الحدث إلى قائمة الأحداث
                match_stats.events.append(event_data)
                match_stats.total_events += 1
                
                # تحديث إحصائيات اللاعبين
                for player_id in participants:
                    if player_id not in match_stats.player_stats:
                        match_stats.player_stats[player_id] = PlayerStats(player_id=player_id)
                    
                    player_stats = match_stats.player_stats[player_id]
                    
                    # تحديث الإحصائيات حسب نوع الحدث
                    if event_type == EventType.PASS.value:
                        player_stats.passes_attempted += 1
                        if event_data.get("successful", True):
                            player_stats.passes_completed += 1
                    
                    elif event_type == EventType.SHOT.value:
                        player_stats.shots += 1
                    
                    elif event_type == EventType.GOAL.value:
                        player_stats.goals += 1
                    
                    elif event_type == EventType.TACKLE.value:
                        player_stats.tackles += 1
                    
                    elif event_type == EventType.FOUL.value:
                        player_stats.fouls_committed += 1
                    
                    elif event_type == EventType.YELLOW_CARD.value:
                        player_stats.yellow_cards += 1
                    
                    elif event_type == EventType.RED_CARD.value:
                        player_stats.red_cards += 1
                    
                    # عد لمسات الكرة
                    if event_type in [EventType.PASS.value, EventType.SHOT.value, 
                                    EventType.DRIBBLE.value]:
                        player_stats.ball_touches += 1
    
    def _calculate_team_stats(self, match_stats: MatchStats):
        """حساب إحصائيات الفرق"""
        # تجميع اللاعبين في فرق (تقدير بسيط)
        team_assignments = self._assign_players_to_teams(match_stats)
        
        for team_id, player_ids in team_assignments.items():
            team_stats = TeamStats(team_id=team_id, team_name=f"Team {team_id}")
            
            # تجميع إحصائيات اللاعبين
            for player_id in player_ids:
                if player_id in match_stats.player_stats:
                    player_stats = match_stats.player_stats[player_id]
                    
                    team_stats.total_passes += player_stats.passes_attempted
                    team_stats.shots += player_stats.shots
                    team_stats.goals += player_stats.goals
                    team_stats.tackles += player_stats.tackles
                    team_stats.fouls += player_stats.fouls_committed
                    team_stats.yellow_cards += player_stats.yellow_cards
                    team_stats.red_cards += player_stats.red_cards
            
            # حساب دقة التمرير للفريق
            total_completed = sum(match_stats.player_stats[pid].passes_completed 
                                for pid in player_ids 
                                if pid in match_stats.player_stats)
            
            if team_stats.total_passes > 0:
                team_stats.pass_accuracy = (total_completed / team_stats.total_passes) * 100
            
            match_stats.team_stats[team_id] = team_stats
    
    def _assign_players_to_teams(self, match_stats: MatchStats) -> Dict[int, List[int]]:
        """تعيين اللاعبين للفرق (تقدير بسيط)"""
        # هذا تنفيذ مبسط - يمكن تحسينه بتحليل ألوان القمصان
        player_ids = list(match_stats.player_stats.keys())
        
        if len(player_ids) <= 11:
            return {1: player_ids}
        
        # تقسيم اللاعبين إلى فريقين
        mid_point = len(player_ids) // 2
        return {
            1: player_ids[:mid_point],
            2: player_ids[mid_point:]
        }
    
    def _generate_heatmaps(self, frame_data: List[Dict[str, Any]], 
                          match_stats: MatchStats, 
                          frame_dimensions: Tuple[int, int]):
        """إنشاء الخرائط الحرارية"""
        heatmap_size = (50, 80)  # حجم الخريطة الحرارية
        
        for player_id in match_stats.player_stats.keys():
            heatmap = np.zeros(heatmap_size)
            
            # جمع مواقع اللاعب
            for frame in frame_data:
                for track_id_str, track_data in frame["tracks"].items():
                    if int(track_id_str) == player_id:
                        # تحويل الموقع إلى إحداثيات الخريطة الحرارية
                        center_x = (track_data["x1"] + track_data["x2"]) / 2
                        center_y = (track_data["y1"] + track_data["y2"]) / 2
                        
                        # تطبيع الإحداثيات
                        norm_x = center_x / frame_dimensions[0]
                        norm_y = center_y / frame_dimensions[1]
                        
                        # تحويل إلى إحداثيات الخريطة الحرارية
                        heatmap_x = int(norm_x * heatmap_size[1])
                        heatmap_y = int(norm_y * heatmap_size[0])
                        
                        # التأكد من الحدود
                        heatmap_x = max(0, min(heatmap_x, heatmap_size[1] - 1))
                        heatmap_y = max(0, min(heatmap_y, heatmap_size[0] - 1))
                        
                        heatmap[heatmap_y, heatmap_x] += 1
            
            # تطبيق تنعيم
            from scipy import ndimage
            heatmap = ndimage.gaussian_filter(heatmap, sigma=1.0)
            
            match_stats.heatmaps[player_id] = heatmap
    
    def _calculate_ratings(self, match_stats: MatchStats):
        """حساب تقييمات الأداء"""
        for player_id, player_stats in match_stats.player_stats.items():
            # التقييم الفني (بناءً على دقة التمرير والأهداف)
            technical_score = 0
            if player_stats.passes_attempted > 0:
                technical_score += player_stats.pass_accuracy * 0.6
            technical_score += player_stats.goals * 10
            technical_score += player_stats.ball_touches * 0.1
            player_stats.technical_rating = min(100, technical_score)
            
            # التقييم البدني (بناءً على المسافة والسرعة)
            physical_score = 0
            physical_score += min(player_stats.total_distance / 100, 100) * 0.5
            physical_score += min(player_stats.max_speed * 5, 50)
            physical_score += player_stats.sprint_count * 2
            player_stats.physical_rating = min(100, physical_score)
            
            # التقييم التكتيكي (بناءً على الإحصائيات الدفاعية)
            tactical_score = 0
            tactical_score += player_stats.tackles * 5
            tactical_score += player_stats.interceptions * 3
            tactical_score += player_stats.clearances * 2
            player_stats.tactical_rating = min(100, tactical_score)
            
            # التقييم النفسي (بناءً على الانضباط)
            mental_score = 100
            mental_score -= player_stats.fouls_committed * 5
            mental_score -= player_stats.yellow_cards * 10
            mental_score -= player_stats.red_cards * 50
            player_stats.mental_rating = max(0, mental_score)
            
            # التقييم الإجمالي
            player_stats.overall_rating = (
                player_stats.technical_rating * 0.3 +
                player_stats.physical_rating * 0.25 +
                player_stats.tactical_rating * 0.25 +
                player_stats.mental_rating * 0.2
            )
    
    def export_statistics(self, match_stats: MatchStats, output_path: str):
        """تصدير الإحصائيات"""
        output_dir = Path(output_path)
        output_dir.mkdir(exist_ok=True)
        
        # تصدير إحصائيات اللاعبين
        player_data = []
        for player_id, stats in match_stats.player_stats.items():
            player_dict = {
                "player_id": player_id,
                "total_distance_km": stats.total_distance / 1000,
                "max_speed_kmh": stats.max_speed * 3.6,
                "average_speed_kmh": stats.average_speed * 3.6,
                "sprint_count": stats.sprint_count,
                "ball_touches": stats.ball_touches,
                "passes_attempted": stats.passes_attempted,
                "passes_completed": stats.passes_completed,
                "pass_accuracy": stats.pass_accuracy,
                "shots": stats.shots,
                "goals": stats.goals,
                "tackles": stats.tackles,
                "fouls": stats.fouls_committed,
                "yellow_cards": stats.yellow_cards,
                "red_cards": stats.red_cards,
                "technical_rating": stats.technical_rating,
                "physical_rating": stats.physical_rating,
                "tactical_rating": stats.tactical_rating,
                "mental_rating": stats.mental_rating,
                "overall_rating": stats.overall_rating
            }
            player_data.append(player_dict)
        
        # حفظ كـ CSV
        df_players = pd.DataFrame(player_data)
        df_players.to_csv(output_dir / "player_statistics.csv", index=False)
        
        # تصدير إحصائيات الفرق
        team_data = []
        for team_id, stats in match_stats.team_stats.items():
            team_dict = {
                "team_id": team_id,
                "team_name": stats.team_name,
                "possession_percentage": stats.possession_percentage,
                "total_passes": stats.total_passes,
                "pass_accuracy": stats.pass_accuracy,
                "shots": stats.shots,
                "goals": stats.goals,
                "tackles": stats.tackles,
                "fouls": stats.fouls,
                "yellow_cards": stats.yellow_cards,
                "red_cards": stats.red_cards
            }
            team_data.append(team_dict)
        
        df_teams = pd.DataFrame(team_data)
        df_teams.to_csv(output_dir / "team_statistics.csv", index=False)
        
        # تصدير الأحداث
        df_events = pd.DataFrame(match_stats.events)
        df_events.to_csv(output_dir / "match_events.csv", index=False)
        
        print(f"تم تصدير الإحصائيات إلى: {output_dir}")
