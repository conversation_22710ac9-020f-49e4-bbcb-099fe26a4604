"""
معالج الفيديو الرئيسي لتحليل مباريات كرة القدم
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Callable
import time
from pathlib import Path
import json
from dataclasses import asdict
from collections import defaultdict

from ..ai_models.base_model import model_manager, ModelType
from ..ai_models.detection.yolo_detector import YOLODetector
from ..ai_models.tracking.deep_sort_tracker import DeepSORTTracker
from ..ai_models.pose_estimation.mediapipe_pose import MediaPipePose
from ..ai_models.event_detection.event_classifier import EventClassifier


class VideoProcessor:
    """معالج الفيديو الرئيسي"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.setup_default_config()
        
        # النماذج
        self.detector = None
        self.tracker = None
        self.pose_estimator = None
        self.event_classifier = None
        
        # بيانات المعالجة
        self.frame_data = []
        self.tracking_data = {}
        self.pose_data = {}
        self.event_data = []
        
        # إحصائيات المعالجة
        self.processing_stats = {
            "total_frames": 0,
            "processed_frames": 0,
            "fps": 0,
            "processing_time": 0,
            "start_time": None
        }
        
    def setup_default_config(self):
        """إعداد التكوين الافتراضي"""
        default_config = {
            "output_dir": "output",
            "save_annotated_video": True,
            "save_raw_data": True,
            "save_statistics": True,
            "frame_skip": 1,  # معالجة كل إطار
            "max_frames": None,  # معالجة جميع الإطارات
            "progress_callback": None,
            "models": {
                "detector": {
                    "confidence_threshold": 0.5,
                    "iou_threshold": 0.45
                },
                "tracker": {
                    "max_age": 30,
                    "min_hits": 3
                },
                "pose_estimator": {
                    "min_detection_confidence": 0.5
                },
                "event_classifier": {
                    "window_size": 30,
                    "confidence_threshold": 0.6
                }
            }
        }
        
        # دمج التكوين المخصص
        for key, value in default_config.items():
            if key not in self.config:
                self.config[key] = value
            elif isinstance(value, dict) and isinstance(self.config[key], dict):
                for sub_key, sub_value in value.items():
                    if sub_key not in self.config[key]:
                        self.config[key][sub_key] = sub_value
    
    def initialize_models(self) -> bool:
        """تهيئة جميع النماذج"""
        try:
            print("تهيئة النماذج...")
            
            # كاشف الكيانات
            self.detector = YOLODetector(config=self.config["models"]["detector"])
            if not self.detector.load_model():
                print("فشل في تحميل كاشف الكيانات")
                return False
            
            # متتبع الكيانات
            self.tracker = DeepSORTTracker(config=self.config["models"]["tracker"])
            if not self.tracker.load_model():
                print("فشل في تحميل متتبع الكيانات")
                return False
            
            # محلل الوضعيات
            self.pose_estimator = MediaPipePose(config=self.config["models"]["pose_estimator"])
            if not self.pose_estimator.load_model():
                print("فشل في تحميل محلل الوضعيات")
                return False
            
            # مصنف الأحداث
            self.event_classifier = EventClassifier(config=self.config["models"]["event_classifier"])
            if not self.event_classifier.load_model():
                print("فشل في تحميل مصنف الأحداث")
                return False
            
            print("تم تهيئة جميع النماذج بنجاح")
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة النماذج: {e}")
            return False
    
    def process_video(self, video_path: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """معالجة فيديو مباراة كاملة"""
        if not self.initialize_models():
            raise RuntimeError("فشل في تهيئة النماذج")
        
        # فتح الفيديو
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"لا يمكن فتح الفيديو: {video_path}")
        
        # معلومات الفيديو
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print(f"معالجة فيديو: {video_path}")
        print(f"الأبعاد: {width}x{height}, FPS: {fps}, الإطارات: {total_frames}")
        
        # إعداد كاتب الفيديو المُحلل
        output_video_path = None
        video_writer = None
        
        if self.config["save_annotated_video"]:
            if output_path:
                output_video_path = output_path
            else:
                output_dir = Path(self.config["output_dir"])
                output_dir.mkdir(exist_ok=True)
                video_name = Path(video_path).stem
                output_video_path = str(output_dir / f"{video_name}_analyzed.mp4")
            
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
        
        # إعداد الإحصائيات
        self.processing_stats["total_frames"] = total_frames
        self.processing_stats["fps"] = fps
        self.processing_stats["start_time"] = time.time()
        
        # معالجة الإطارات
        frame_id = 0
        processed_count = 0
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # تخطي الإطارات إذا لزم الأمر
                if frame_id % self.config["frame_skip"] != 0:
                    frame_id += 1
                    continue
                
                # حد أقصى للإطارات
                if (self.config["max_frames"] and 
                    processed_count >= self.config["max_frames"]):
                    break
                
                # معالجة الإطار
                timestamp = frame_id / fps
                annotated_frame = self.process_frame(frame, frame_id, timestamp)
                
                # حفظ الإطار المُحلل
                if video_writer:
                    video_writer.write(annotated_frame)
                
                processed_count += 1
                frame_id += 1
                
                # تحديث التقدم
                if self.config["progress_callback"]:
                    progress = processed_count / total_frames * 100
                    self.config["progress_callback"](progress, frame_id, total_frames)
                
                # طباعة التقدم
                if processed_count % 100 == 0:
                    progress = processed_count / total_frames * 100
                    print(f"التقدم: {progress:.1f}% ({processed_count}/{total_frames})")
        
        finally:
            cap.release()
            if video_writer:
                video_writer.release()
        
        # حساب الإحصائيات النهائية
        end_time = time.time()
        self.processing_stats["processing_time"] = end_time - self.processing_stats["start_time"]
        self.processing_stats["processed_frames"] = processed_count
        
        # حفظ البيانات
        results = self.save_results(video_path, output_video_path)
        
        print(f"تمت المعالجة بنجاح!")
        print(f"الإطارات المعالجة: {processed_count}")
        print(f"وقت المعالجة: {self.processing_stats['processing_time']:.2f} ثانية")
        print(f"متوسط FPS: {processed_count / self.processing_stats['processing_time']:.2f}")
        
        return results
    
    def process_frame(self, frame: np.ndarray, frame_id: int, timestamp: float) -> np.ndarray:
        """معالجة إطار واحد"""
        annotated_frame = frame.copy()
        
        # 1. كشف الكيانات
        detection_result = self.detector.predict(
            frame, 
            frame_id=frame_id, 
            timestamp=timestamp
        )
        
        # تصفية الكشوفات
        filtered_detections = self.detector.filter_detections(detection_result.bounding_boxes)
        
        # 2. تتبع الكيانات
        tracking_result = self.tracker.predict(
            frame, 
            filtered_detections,
            frame_id=frame_id,
            timestamp=timestamp
        )
        
        # 3. تحليل الوضعيات للاعبين
        player_bboxes = [bbox for bbox in filtered_detections 
                        if bbox.class_name in ["player", "goalkeeper"]]
        
        pose_results = []
        if player_bboxes:
            player_ids = [track_id for track_id, bbox in tracking_result.tracks.items()]
            pose_results = self.pose_estimator.predict_multiple(
                frame, 
                player_bboxes,
                frame_id=frame_id,
                timestamp=timestamp,
                person_ids=player_ids[:len(player_bboxes)]
            )
        
        # 4. تحديث بيانات الأحداث
        ball_pos = self._get_ball_position(filtered_detections)
        player_positions = {track_id: bbox.center for track_id, bbox in tracking_result.tracks.items()}
        player_actions = {pose.person_id: self.pose_estimator.analyze_action(pose).value 
                         for pose in pose_results}
        
        self.event_classifier.update_temporal_data(ball_pos, player_positions, player_actions)
        
        # 5. تصنيف الأحداث
        event_results = self.event_classifier.predict(
            frame,
            frame_id=frame_id,
            timestamp=timestamp
        )
        
        # 6. حفظ البيانات
        self._store_frame_data(frame_id, timestamp, detection_result, tracking_result, 
                              pose_results, event_results)
        
        # 7. رسم التعليقات التوضيحية
        annotated_frame = self._annotate_frame(
            annotated_frame, filtered_detections, tracking_result.tracks, 
            pose_results, event_results
        )
        
        return annotated_frame
    
    def _get_ball_position(self, detections: List) -> Optional[Tuple[float, float]]:
        """الحصول على موقع الكرة"""
        for detection in detections:
            if detection.class_name == "ball":
                return detection.center
        return None
    
    def _store_frame_data(self, frame_id: int, timestamp: float, 
                         detection_result, tracking_result, pose_results, event_results):
        """حفظ بيانات الإطار"""
        frame_data = {
            "frame_id": frame_id,
            "timestamp": timestamp,
            "detections": [asdict(det) for det in detection_result.bounding_boxes],
            "tracks": {str(k): asdict(v) for k, v in tracking_result.tracks.items()},
            "poses": [asdict(pose) for pose in pose_results],
            "events": [asdict(event) for event in event_results]
        }
        
        self.frame_data.append(frame_data)
    
    def _annotate_frame(self, frame: np.ndarray, detections, tracks, poses, events) -> np.ndarray:
        """إضافة التعليقات التوضيحية للإطار"""
        annotated_frame = frame.copy()
        
        # رسم الكشوفات
        annotated_frame = self.detector.draw_detections(annotated_frame, detections)
        
        # رسم مسارات التتبع
        for track_id, bbox in tracks.items():
            # رسم ID المسار
            cv2.putText(
                annotated_frame,
                f"ID: {track_id}",
                (int(bbox.x1), int(bbox.y1) - 30),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.6,
                (255, 255, 255),
                2
            )
        
        # رسم الوضعيات
        for pose in poses:
            annotated_frame = self.pose_estimator.draw_pose(annotated_frame, pose)
            
            # رسم نوع الحركة
            action = self.pose_estimator.analyze_action(pose)
            cv2.putText(
                annotated_frame,
                action.value,
                (int(pose.bbox.x1), int(pose.bbox.y2) + 20),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                (0, 255, 255),
                2
            )
        
        # رسم الأحداث
        for event in events:
            if event.confidence > 0.7:  # عرض الأحداث عالية الثقة فقط
                cv2.putText(
                    annotated_frame,
                    f"{event.event_type}: {event.confidence:.2f}",
                    (50, 50),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    1.0,
                    (0, 0, 255),
                    2
                )
        
        return annotated_frame
    
    def save_results(self, input_video_path: str, output_video_path: Optional[str]) -> Dict[str, Any]:
        """حفظ نتائج التحليل"""
        output_dir = Path(self.config["output_dir"])
        output_dir.mkdir(exist_ok=True)
        
        video_name = Path(input_video_path).stem
        results = {
            "input_video": input_video_path,
            "output_video": output_video_path,
            "processing_stats": self.processing_stats,
            "frame_data": self.frame_data
        }
        
        # حفظ البيانات الخام
        if self.config["save_raw_data"]:
            raw_data_path = output_dir / f"{video_name}_raw_data.json"
            with open(raw_data_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            results["raw_data_file"] = str(raw_data_path)
        
        # حفظ الإحصائيات
        if self.config["save_statistics"]:
            stats_path = output_dir / f"{video_name}_statistics.json"
            statistics = self._calculate_statistics()
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(statistics, f, ensure_ascii=False, indent=2, default=str)
            results["statistics_file"] = str(stats_path)
        
        return results
    
    def _calculate_statistics(self) -> Dict[str, Any]:
        """حساب الإحصائيات من البيانات المعالجة"""
        stats = {
            "total_detections": 0,
            "total_tracks": 0,
            "total_events": 0,
            "event_counts": defaultdict(int),
            "player_stats": defaultdict(dict),
            "ball_possession": [],
            "average_players_per_frame": 0
        }
        
        # حساب الإحصائيات من بيانات الإطارات
        total_players = 0
        
        for frame_data in self.frame_data:
            stats["total_detections"] += len(frame_data["detections"])
            stats["total_tracks"] += len(frame_data["tracks"])
            stats["total_events"] += len(frame_data["events"])
            
            # عد الأحداث
            for event in frame_data["events"]:
                stats["event_counts"][event["event_type"]] += 1
            
            # عد اللاعبين
            players_in_frame = len([d for d in frame_data["detections"] 
                                  if d["class_name"] in ["player", "goalkeeper"]])
            total_players += players_in_frame
        
        if self.frame_data:
            stats["average_players_per_frame"] = total_players / len(self.frame_data)
        
        return dict(stats)
