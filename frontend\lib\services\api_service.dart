import 'dart:io';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../models/match.dart';
import '../models/player.dart';

part 'api_service.g.dart';

@RestApi(baseUrl: "http://localhost:8000")
abstract class ApiService {
  factory ApiService(Dio dio, {String baseUrl}) = _ApiService;

  // Matches endpoints
  @GET("/matches/")
  Future<List<Match>> getMatches({
    @Query("limit") int limit = 100,
    @Query("offset") int offset = 0,
  });

  @POST("/matches/")
  Future<Match> createMatch(@Body() Map<String, dynamic> matchData);

  @GET("/matches/{id}")
  Future<Match> getMatch(@Path("id") String id);

  @DELETE("/matches/{id}")
  Future<void> deleteMatch(@Path("id") String id);

  @POST("/matches/{id}/upload-video")
  @MultiPart()
  Future<Map<String, dynamic>> uploadVideo(
    @Path("id") String matchId,
    @Part() File file,
  );

  // Statistics endpoints
  @GET("/matches/{id}/statistics")
  Future<Map<String, dynamic>> getMatchStatistics(@Path("id") String id);

  @GET("/matches/{id}/events")
  Future<Map<String, dynamic>> getMatchEvents(
    @Path("id") String id,
    @Query("event_type") String? eventType,
    @Query("limit") int limit = 100,
    @Query("offset") int offset = 0,
  );

  @GET("/matches/{matchId}/heatmap/{playerId}")
  Future<Map<String, dynamic>> getPlayerHeatmap(
    @Path("matchId") String matchId,
    @Path("playerId") String playerId,
  );

  // Export endpoints
  @GET("/matches/{id}/export/{format}")
  Future<Response> exportMatchData(
    @Path("id") String id,
    @Path("format") String format,
  );

  // Health check
  @GET("/health")
  Future<Map<String, dynamic>> healthCheck();
}

class ApiClient {
  static const String baseUrl = 'http://localhost:8000';
  late final Dio _dio;
  late final ApiService _apiService;

  ApiClient() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print(obj),
    ));

    _apiService = ApiService(_dio);
  }

  ApiService get api => _apiService;

  // Custom methods for complex operations
  Future<List<Match>> getMatches({int limit = 100, int offset = 0}) async {
    try {
      return await _apiService.getMatches(limit: limit, offset: offset);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<Match> createMatch({
    required String name,
    DateTime? date,
    double? duration,
  }) async {
    try {
      final data = {
        'name': name,
        if (date != null) 'date': date.toIso8601String(),
        if (duration != null) 'duration': duration,
      };
      return await _apiService.createMatch(data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<Match> getMatch(String id) async {
    try {
      return await _apiService.getMatch(id);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<Map<String, dynamic>> uploadVideo(String matchId, File videoFile) async {
    try {
      return await _apiService.uploadVideo(matchId, videoFile);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<Map<String, dynamic>> getMatchStatistics(String matchId) async {
    try {
      return await _apiService.getMatchStatistics(matchId);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<List<Player>> getMatchPlayers(String matchId) async {
    try {
      final stats = await getMatchStatistics(matchId);
      final playersData = stats['players'] as List<dynamic>? ?? [];
      return playersData.map((data) => Player.fromJson(data)).toList();
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<bool> checkHealth() async {
    try {
      final response = await _apiService.healthCheck();
      return response['status'] == 'healthy';
    } catch (e) {
      return false;
    }
  }

  Exception _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return Exception('انتهت مهلة الاتصال. تحقق من اتصال الإنترنت.');
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final message = error.response?.data?['detail'] ?? 'خطأ في الخادم';
          return Exception('خطأ $statusCode: $message');
        case DioExceptionType.cancel:
          return Exception('تم إلغاء الطلب');
        case DioExceptionType.connectionError:
          return Exception('خطأ في الاتصال. تأكد من تشغيل الخادم.');
        default:
          return Exception('خطأ غير متوقع: ${error.message}');
      }
    }
    return Exception('خطأ غير معروف: $error');
  }
}
