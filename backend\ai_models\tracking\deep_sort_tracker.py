"""
نموذج تتبع الكيانات باستخدام DeepSORT
"""

import numpy as np
import cv2
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict, deque
import torch
import torch.nn as nn
from scipy.spatial.distance import cosine
from filterpy.kalman import KalmanFilter

from ..base_model import BaseModel, ModelType, BoundingBox, TrackingResult


class Track:
    """فئة تمثل مسار واحد"""
    
    def __init__(self, track_id: int, bbox: BoundingBox, feature: np.ndarray):
        self.track_id = track_id
        self.bbox = bbox
        self.feature = feature
        self.age = 0
        self.hits = 1
        self.time_since_update = 0
        self.state = "tentative"  # tentative, confirmed, deleted
        
        # مرشح كالمان للتنبؤ بالموقع
        self.kf = self._init_kalman_filter(bbox)
        
        # تاريخ المواقع
        self.history = deque(maxlen=30)
        self.history.append((bbox.center, bbox))
        
        # معلومات إضافية
        self.class_name = bbox.class_name
        self.team_id = None
        self.player_number = None
        
    def _init_kalman_filter(self, bbox: BoundingBox) -> KalmanFilter:
        """تهيئة مرشح كالمان للتتبع"""
        kf = KalmanFilter(dim_x=8, dim_z=4)
        
        # متغيرات الحالة: [x, y, w, h, vx, vy, vw, vh]
        kf.x = np.array([bbox.center[0], bbox.center[1], bbox.width, bbox.height, 0, 0, 0, 0])
        
        # مصفوفة الانتقال
        kf.F = np.array([
            [1, 0, 0, 0, 1, 0, 0, 0],
            [0, 1, 0, 0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0, 0, 1, 0],
            [0, 0, 0, 1, 0, 0, 0, 1],
            [0, 0, 0, 0, 1, 0, 0, 0],
            [0, 0, 0, 0, 0, 1, 0, 0],
            [0, 0, 0, 0, 0, 0, 1, 0],
            [0, 0, 0, 0, 0, 0, 0, 1]
        ])
        
        # مصفوفة القياس
        kf.H = np.array([
            [1, 0, 0, 0, 0, 0, 0, 0],
            [0, 1, 0, 0, 0, 0, 0, 0],
            [0, 0, 1, 0, 0, 0, 0, 0],
            [0, 0, 0, 1, 0, 0, 0, 0]
        ])
        
        # ضوضاء العملية
        kf.Q *= 0.01
        
        # ضوضاء القياس
        kf.R *= 0.1
        
        return kf
    
    def predict(self):
        """التنبؤ بالموقع التالي"""
        self.kf.predict()
        self.age += 1
        self.time_since_update += 1
        
    def update(self, bbox: BoundingBox, feature: np.ndarray):
        """تحديث المسار بكشف جديد"""
        # تحديث مرشح كالمان
        measurement = np.array([bbox.center[0], bbox.center[1], bbox.width, bbox.height])
        self.kf.update(measurement)
        
        # تحديث المعلومات
        self.bbox = bbox
        self.feature = feature
        self.hits += 1
        self.time_since_update = 0
        
        # إضافة إلى التاريخ
        self.history.append((bbox.center, bbox))
        
        # تحديث الحالة
        if self.state == "tentative" and self.hits >= 3:
            self.state = "confirmed"
    
    def get_predicted_bbox(self) -> BoundingBox:
        """الحصول على المربع المتوقع"""
        state = self.kf.x
        x, y, w, h = state[0], state[1], state[2], state[3]
        
        return BoundingBox(
            x1=x - w/2,
            y1=y - h/2,
            x2=x + w/2,
            y2=y + h/2,
            confidence=self.bbox.confidence,
            class_id=self.bbox.class_id,
            class_name=self.bbox.class_name
        )
    
    def is_confirmed(self) -> bool:
        """هل المسار مؤكد؟"""
        return self.state == "confirmed"
    
    def is_deleted(self) -> bool:
        """هل المسار محذوف؟"""
        return self.state == "deleted"
    
    def mark_missed(self):
        """تسجيل فقدان الكشف"""
        if self.state == "tentative":
            self.state = "deleted"
        elif self.time_since_update > 30:  # 30 إطار بدون تحديث
            self.state = "deleted"


class FeatureExtractor(nn.Module):
    """مستخرج الميزات للتتبع"""
    
    def __init__(self):
        super().__init__()
        self.backbone = nn.Sequential(
            nn.Conv2d(3, 32, 3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(32, 64, 3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(64, 128, 3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Flatten(),
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Linear(256, 128)
        )
    
    def forward(self, x):
        return self.backbone(x)


class DeepSORTTracker(BaseModel):
    """متتبع DeepSORT"""
    
    def __init__(self, model_path: Optional[str] = None, config: Optional[Dict] = None):
        default_config = {
            "max_age": 30,
            "min_hits": 3,
            "iou_threshold": 0.3,
            "feature_threshold": 0.6,
            "input_size": (64, 128),  # (width, height)
            "device": "cuda" if torch.cuda.is_available() else "cpu"
        }
        
        if config:
            default_config.update(config)
            
        super().__init__(model_path, default_config)
        
        self.tracks: List[Track] = []
        self.next_id = 1
        self.feature_extractor = None
        
    def _get_model_type(self) -> ModelType:
        return ModelType.TRACKING
    
    def load_model(self) -> bool:
        """تحميل نموذج استخراج الميزات"""
        try:
            self.feature_extractor = FeatureExtractor()
            
            if self.model_path:
                # تحميل أوزان مدربة مسبقاً
                checkpoint = torch.load(self.model_path, map_location=self.config["device"])
                self.feature_extractor.load_state_dict(checkpoint)
            
            self.feature_extractor.to(self.config["device"])
            self.feature_extractor.eval()
            
            self.is_loaded = True
            print(f"تم تحميل متتبع DeepSORT بنجاح على {self.config['device']}")
            return True
            
        except Exception as e:
            print(f"خطأ في تحميل متتبع DeepSORT: {e}")
            self.is_loaded = False
            return False
    
    def extract_features(self, frame: np.ndarray, bboxes: List[BoundingBox]) -> List[np.ndarray]:
        """استخراج الميزات من المناطق المكتشفة"""
        if not self.is_loaded:
            return [np.random.rand(128) for _ in bboxes]  # ميزات عشوائية كبديل
        
        features = []
        
        for bbox in bboxes:
            # قص المنطقة
            x1, y1, x2, y2 = int(bbox.x1), int(bbox.y1), int(bbox.x2), int(bbox.y2)
            crop = frame[y1:y2, x1:x2]
            
            if crop.size == 0:
                features.append(np.random.rand(128))
                continue
            
            # تغيير الحجم
            crop_resized = cv2.resize(crop, self.config["input_size"])
            
            # تحويل إلى tensor
            crop_tensor = torch.from_numpy(crop_resized).permute(2, 0, 1).float() / 255.0
            crop_tensor = crop_tensor.unsqueeze(0).to(self.config["device"])
            
            # استخراج الميزات
            with torch.no_grad():
                feature = self.feature_extractor(crop_tensor)
                feature = feature.cpu().numpy().flatten()
                
            features.append(feature)
        
        return features
    
    def predict(self, frame: np.ndarray, detections: List[BoundingBox], **kwargs) -> TrackingResult:
        """تتبع الكيانات في الإطار"""
        frame_id = kwargs.get("frame_id", 0)
        timestamp = kwargs.get("timestamp", 0.0)
        
        # التنبؤ بمواقع المسارات الحالية
        for track in self.tracks:
            track.predict()
        
        # استخراج الميزات من الكشوفات الجديدة
        features = self.extract_features(frame, detections)
        
        # مطابقة الكشوفات مع المسارات
        matched_tracks, unmatched_detections, unmatched_tracks = self._associate_detections_to_tracks(
            detections, features
        )
        
        # تحديث المسارات المطابقة
        for track_idx, det_idx in matched_tracks:
            self.tracks[track_idx].update(detections[det_idx], features[det_idx])
        
        # إنشاء مسارات جديدة للكشوفات غير المطابقة
        for det_idx in unmatched_detections:
            self._create_new_track(detections[det_idx], features[det_idx])
        
        # تسجيل المسارات المفقودة
        for track_idx in unmatched_tracks:
            self.tracks[track_idx].mark_missed()
        
        # إزالة المسارات المحذوفة
        self.tracks = [track for track in self.tracks if not track.is_deleted()]
        
        # إنشاء النتيجة
        tracks_dict = {}
        for track in self.tracks:
            if track.is_confirmed():
                tracks_dict[track.track_id] = track.bbox
        
        return TrackingResult(
            frame_id=frame_id,
            timestamp=timestamp,
            tracks=tracks_dict,
            frame_shape=frame.shape
        )
    
    def _associate_detections_to_tracks(self, detections: List[BoundingBox], 
                                      features: List[np.ndarray]) -> Tuple[List[Tuple[int, int]], List[int], List[int]]:
        """مطابقة الكشوفات مع المسارات"""
        if len(self.tracks) == 0:
            return [], list(range(len(detections))), []
        
        # حساب مصفوفة التكلفة
        cost_matrix = self._compute_cost_matrix(detections, features)
        
        # حل مشكلة التخصيص المجري
        matched_indices = self._hungarian_assignment(cost_matrix)
        
        # تصنيف النتائج
        matched_tracks = []
        unmatched_detections = list(range(len(detections)))
        unmatched_tracks = list(range(len(self.tracks)))
        
        for track_idx, det_idx in matched_indices:
            if cost_matrix[track_idx, det_idx] < self.config["feature_threshold"]:
                matched_tracks.append((track_idx, det_idx))
                unmatched_detections.remove(det_idx)
                unmatched_tracks.remove(track_idx)
        
        return matched_tracks, unmatched_detections, unmatched_tracks
    
    def _compute_cost_matrix(self, detections: List[BoundingBox], 
                           features: List[np.ndarray]) -> np.ndarray:
        """حساب مصفوفة التكلفة"""
        cost_matrix = np.zeros((len(self.tracks), len(detections)))
        
        for i, track in enumerate(self.tracks):
            for j, (detection, feature) in enumerate(zip(detections, features)):
                # تكلفة المسافة الهندسية (IoU)
                iou_cost = 1 - self._compute_iou(track.get_predicted_bbox(), detection)
                
                # تكلفة المسافة في الميزات
                feature_cost = cosine(track.feature, feature)
                
                # التكلفة المجمعة
                cost_matrix[i, j] = 0.5 * iou_cost + 0.5 * feature_cost
        
        return cost_matrix
    
    def _compute_iou(self, bbox1: BoundingBox, bbox2: BoundingBox) -> float:
        """حساب Intersection over Union"""
        # منطقة التقاطع
        x1 = max(bbox1.x1, bbox2.x1)
        y1 = max(bbox1.y1, bbox2.y1)
        x2 = min(bbox1.x2, bbox2.x2)
        y2 = min(bbox1.y2, bbox2.y2)
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        intersection = (x2 - x1) * (y2 - y1)
        union = bbox1.area + bbox2.area - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def _hungarian_assignment(self, cost_matrix: np.ndarray) -> List[Tuple[int, int]]:
        """حل مشكلة التخصيص المجري (تنفيذ مبسط)"""
        # هذا تنفيذ مبسط - في الواقع يجب استخدام scipy.optimize.linear_sum_assignment
        matched = []
        
        for i in range(cost_matrix.shape[0]):
            min_cost_idx = np.argmin(cost_matrix[i])
            if cost_matrix[i, min_cost_idx] < self.config["feature_threshold"]:
                matched.append((i, min_cost_idx))
        
        return matched
    
    def _create_new_track(self, detection: BoundingBox, feature: np.ndarray):
        """إنشاء مسار جديد"""
        track = Track(self.next_id, detection, feature)
        self.tracks.append(track)
        self.next_id += 1
    
    def get_track_history(self, track_id: int) -> List[Tuple[float, float]]:
        """الحصول على تاريخ مسار معين"""
        for track in self.tracks:
            if track.track_id == track_id:
                return [pos for pos, _ in track.history]
        return []
