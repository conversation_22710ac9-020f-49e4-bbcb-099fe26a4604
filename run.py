#!/usr/bin/env python3
"""
سكريبت تشغيل سريع لنظام Taktik
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات...")
    
    # فحص Python
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    print(f"✅ Python {sys.version.split()[0]}")
    
    # فحص pip
    try:
        import pip
        print(f"✅ pip متوفر")
    except ImportError:
        print("❌ pip غير متوفر")
        return False
    
    # فحص FFmpeg
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FFmpeg متوفر")
        else:
            print("⚠️  FFmpeg غير متوفر - قد تواجه مشاكل في معالجة الفيديو")
    except FileNotFoundError:
        print("⚠️  FFmpeg غير مثبت - قد تواجه مشاكل في معالجة الفيديو")
    
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def setup_environment():
    """إعداد البيئة"""
    print("🔧 إعداد البيئة...")
    
    # إنشاء المجلدات
    directories = [
        "uploads", "output", "static", "logs", 
        "models", "data", "data/videos", "data/datasets"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ تم إعداد البيئة")

def init_database():
    """تهيئة قاعدة البيانات"""
    print("🗄️  تهيئة قاعدة البيانات...")
    
    try:
        subprocess.check_call([
            sys.executable, 'backend/main.py', 'init', '--sample'
        ])
        print("✅ تم تهيئة قاعدة البيانات")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تهيئة قاعدة البيانات: {e}")
        return False

def run_server(host="0.0.0.0", port=8000, reload=False):
    """تشغيل الخادم"""
    print(f"🚀 تشغيل الخادم على {host}:{port}")
    
    cmd = [sys.executable, 'backend/main.py', 'server', 
           '--host', host, '--port', str(port)]
    
    if reload:
        cmd.append('--reload')
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم")

def run_tests():
    """تشغيل الاختبارات"""
    print("🧪 تشغيل الاختبارات...")
    
    try:
        subprocess.check_call([sys.executable, '-m', 'pytest'])
        print("✅ نجحت جميع الاختبارات")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشلت بعض الاختبارات: {e}")
        return False

def process_video(video_path, output_dir=None):
    """معالجة فيديو"""
    if not Path(video_path).exists():
        print(f"❌ الفيديو غير موجود: {video_path}")
        return False
    
    print(f"🎥 معالجة الفيديو: {video_path}")
    
    cmd = [sys.executable, 'backend/main.py', 'process', video_path]
    if output_dir:
        cmd.extend(['--output', output_dir])
    
    try:
        subprocess.check_call(cmd)
        print("✅ تمت معالجة الفيديو بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في معالجة الفيديو: {e}")
        return False

def docker_build():
    """بناء صورة Docker"""
    print("🐳 بناء صورة Docker...")
    
    try:
        subprocess.check_call(['docker', 'build', '-t', 'taktik', '.'])
        print("✅ تم بناء صورة Docker بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في بناء صورة Docker: {e}")
        return False

def docker_run():
    """تشغيل Docker Compose"""
    print("🐳 تشغيل Docker Compose...")
    
    try:
        subprocess.check_call(['docker-compose', 'up', '-d'])
        print("✅ تم تشغيل الخدمات بنجاح")
        print("🌐 الخادم متاح على: http://localhost")
        print("📊 Grafana متاح على: http://localhost:3000")
        print("🌸 Flower متاح على: http://localhost:5555")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تشغيل Docker Compose: {e}")
        return False

def docker_stop():
    """إيقاف Docker Compose"""
    print("🐳 إيقاف Docker Compose...")
    
    try:
        subprocess.check_call(['docker-compose', 'down'])
        print("✅ تم إيقاف الخدمات")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في إيقاف Docker Compose: {e}")
        return False

def show_info():
    """عرض معلومات النظام"""
    try:
        subprocess.check_call([sys.executable, 'backend/main.py', 'info'])
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في عرض المعلومات: {e}")

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(
        description="سكريبت تشغيل سريع لنظام Taktik",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python run.py setup                          # إعداد النظام
  python run.py server                         # تشغيل الخادم
  python run.py server --port 8080             # تشغيل الخادم على منفذ مخصص
  python run.py process video.mp4              # معالجة فيديو
  python run.py test                           # تشغيل الاختبارات
  python run.py docker-build                   # بناء صورة Docker
  python run.py docker-run                     # تشغيل Docker Compose
  python run.py docker-stop                    # إيقاف Docker Compose
  python run.py info                           # عرض معلومات النظام
        """
    )
    
    subparsers = parser.add_subparsers(dest="command", help="الأوامر المتاحة")
    
    # أمر الإعداد
    setup_parser = subparsers.add_parser("setup", help="إعداد النظام")
    setup_parser.add_argument("--skip-install", action="store_true", 
                             help="تخطي تثبيت المتطلبات")
    
    # أمر تشغيل الخادم
    server_parser = subparsers.add_parser("server", help="تشغيل الخادم")
    server_parser.add_argument("--host", default="0.0.0.0", help="عنوان الخادم")
    server_parser.add_argument("--port", type=int, default=8000, help="منفذ الخادم")
    server_parser.add_argument("--reload", action="store_true", 
                              help="إعادة التحميل التلقائي")
    
    # أمر معالجة الفيديو
    process_parser = subparsers.add_parser("process", help="معالجة فيديو")
    process_parser.add_argument("video_path", help="مسار ملف الفيديو")
    process_parser.add_argument("--output", help="مجلد المخرجات")
    
    # أمر الاختبارات
    test_parser = subparsers.add_parser("test", help="تشغيل الاختبارات")
    
    # أوامر Docker
    docker_build_parser = subparsers.add_parser("docker-build", help="بناء صورة Docker")
    docker_run_parser = subparsers.add_parser("docker-run", help="تشغيل Docker Compose")
    docker_stop_parser = subparsers.add_parser("docker-stop", help="إيقاف Docker Compose")
    
    # أمر المعلومات
    info_parser = subparsers.add_parser("info", help="عرض معلومات النظام")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # فحص المتطلبات الأساسية
    if not check_requirements():
        print("❌ فشل في فحص المتطلبات")
        return
    
    if args.command == "setup":
        setup_environment()
        if not args.skip_install:
            if not install_requirements():
                return
        if not init_database():
            return
        print("🎉 تم إعداد النظام بنجاح!")
        print("💡 يمكنك الآن تشغيل الخادم باستخدام: python run.py server")
    
    elif args.command == "server":
        run_server(args.host, args.port, args.reload)
    
    elif args.command == "process":
        process_video(args.video_path, args.output)
    
    elif args.command == "test":
        run_tests()
    
    elif args.command == "docker-build":
        docker_build()
    
    elif args.command == "docker-run":
        docker_run()
    
    elif args.command == "docker-stop":
        docker_stop()
    
    elif args.command == "info":
        show_info()
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
