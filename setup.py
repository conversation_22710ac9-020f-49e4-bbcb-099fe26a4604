"""
إعداد حزمة Taktik
"""

from setuptools import setup, find_packages
import os

# قراءة README
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# قراءة المتطلبات
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="taktik",
    version="1.0.0",
    author="Taktik Team",
    author_email="<EMAIL>",
    description="نظام ذكاء اصطناعي متكامل لتحليل مباريات كرة القدم",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/taktik",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Multimedia :: Video :: Display",
        "Topic :: Games/Entertainment",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=6.2.0",
            "pytest-asyncio>=0.15.0",
            "pytest-cov>=2.12.0",
            "black>=21.7.0",
            "flake8>=3.9.0",
            "isort>=5.9.0",
            "mypy>=0.910",
        ],
        "gpu": [
            "torch>=1.9.0+cu111",
            "torchvision>=0.10.0+cu111",
        ],
        "docs": [
            "sphinx>=4.0.0",
            "sphinx-rtd-theme>=0.5.0",
            "myst-parser>=0.15.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "taktik=backend.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "taktik": [
            "data/*",
            "models/*",
            "static/*",
            "templates/*",
        ],
    },
    zip_safe=False,
    keywords=[
        "football",
        "soccer", 
        "video analysis",
        "computer vision",
        "artificial intelligence",
        "sports analytics",
        "player tracking",
        "tactical analysis"
    ],
    project_urls={
        "Bug Reports": "https://github.com/your-username/taktik/issues",
        "Source": "https://github.com/your-username/taktik",
        "Documentation": "https://taktik.readthedocs.io/",
    },
)
