"""
واجهة برمجة التطبيقات الرئيسية لنظام تحليل مباريات كرة القدم
"""

from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import os
import uuid
import time
from pathlib import Path
import json

from ..database.database import db_manager
from ..data_processing.video_processor import VideoProcessor
from ..analytics.match_analyzer import MatchAnalyzer
from .schemas import *

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="Taktik - نظام تحليل مباريات كرة القدم",
    description="نظام ذكاء اصطناعي متكامل لتحليل مباريات كرة القدم",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # في الإنتاج، حدد النطاقات المسموحة
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إعداد الملفات الثابتة
static_dir = Path("static")
static_dir.mkdir(exist_ok=True)
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# إعداد مجلد التحميل
upload_dir = Path("uploads")
upload_dir.mkdir(exist_ok=True)

# إعداد مجلد المخرجات
output_dir = Path("output")
output_dir.mkdir(exist_ok=True)


# ==================== نقاط النهاية الأساسية ====================

@app.get("/")
async def root():
    """الصفحة الرئيسية"""
    return {
        "message": "مرحباً بك في نظام Taktik لتحليل مباريات كرة القدم",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """فحص صحة النظام"""
    try:
        # فحص قاعدة البيانات
        stats = db_manager.get_database_stats()

        return {
            "status": "healthy",
            "database": "connected",
            "stats": stats
        }
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e)
            }
        )


# ==================== إدارة المباريات ====================

@app.post("/matches/", response_model=MatchResponse)
async def create_match(match_data: MatchCreate):
    """إنشاء مباراة جديدة"""
    try:
        match_dict = match_data.model_dump()
        match_id = db_manager.create_match(match_dict)

        # إرجاع استجابة مبسطة
        from datetime import datetime
        return MatchResponse(
            id=match_id,
            name=match_dict['name'],
            date=match_dict.get('date'),
            duration=match_dict.get('duration'),
            video_path=None,
            video_fps=None,
            video_width=None,
            video_height=None,
            processing_status='pending',
            processing_progress=0.0,
            total_frames=None,
            processed_frames=None,
            total_events=None,
            ball_in_play_time=None,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/matches/", response_model=List[MatchResponse])
async def get_matches(limit: int = 100, offset: int = 0):
    """الحصول على قائمة المباريات"""
    try:
        # استخدام استعلام مباشر لتجنب مشاكل session
        with db_manager.get_session() as session:
            from ..database.models import Match
            matches = session.query(Match).offset(offset).limit(limit).all()

            # تحويل إلى قائمة dict
            matches_list = []
            for match in matches:
                match_dict = {
                    'id': match.id,
                    'name': match.name,
                    'date': match.date,
                    'duration': match.duration,
                    'video_path': match.video_path,
                    'video_fps': match.video_fps,
                    'video_width': match.video_width,
                    'video_height': match.video_height,
                    'processing_status': match.processing_status,
                    'processing_progress': match.processing_progress,
                    'total_frames': match.total_frames,
                    'processed_frames': match.processed_frames,
                    'total_events': match.total_events,
                    'ball_in_play_time': match.ball_in_play_time,
                    'created_at': match.created_at,
                    'updated_at': match.updated_at
                }
                matches_list.append(MatchResponse(**match_dict))

            return matches_list

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/matches/{match_id}", response_model=MatchResponse)
async def get_match(match_id: str):
    """الحصول على مباراة محددة"""
    try:
        with db_manager.get_session() as session:
            from ..database.models import Match
            match = session.query(Match).filter(Match.id == match_id).first()

            if not match:
                raise HTTPException(status_code=404, detail="المباراة غير موجودة")

            match_dict = {
                'id': match.id,
                'name': match.name,
                'date': match.date,
                'duration': match.duration,
                'video_path': match.video_path,
                'video_fps': match.video_fps,
                'video_width': match.video_width,
                'video_height': match.video_height,
                'processing_status': match.processing_status,
                'processing_progress': match.processing_progress,
                'total_frames': match.total_frames,
                'processed_frames': match.processed_frames,
                'total_events': match.total_events,
                'ball_in_play_time': match.ball_in_play_time,
                'created_at': match.created_at,
                'updated_at': match.updated_at
            }

            return MatchResponse(**match_dict)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/matches/{match_id}")
async def delete_match(match_id: str):
    """حذف مباراة"""
    try:
        success = db_manager.delete_match(match_id)
        if not success:
            raise HTTPException(status_code=404, detail="المباراة غير موجودة")

        return {"message": "تم حذف المباراة بنجاح"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ==================== تحميل ومعالجة الفيديو ====================

@app.post("/matches/{match_id}/upload-video")
async def upload_video(
    match_id: str,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...)
):
    """تحميل فيديو المباراة"""
    try:
        # التحقق من وجود المباراة
        match = db_manager.get_match(match_id)
        if not match:
            raise HTTPException(status_code=404, detail="المباراة غير موجودة")

        # التحقق من نوع الملف
        if not file.content_type.startswith("video/"):
            raise HTTPException(status_code=400, detail="يجب أن يكون الملف فيديو")

        # حفظ الفيديو
        file_extension = Path(file.filename).suffix
        video_filename = f"{match_id}_{uuid.uuid4()}{file_extension}"
        video_path = upload_dir / video_filename

        with open(video_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # تحديث مسار الفيديو في قاعدة البيانات
        db_manager.update_match(match_id, {"video_path": str(video_path)})

        # بدء معالجة الفيديو في الخلفية
        background_tasks.add_task(process_video_background, match_id, str(video_path))

        return {
            "message": "تم تحميل الفيديو بنجاح وبدأت المعالجة",
            "video_path": str(video_path),
            "match_id": match_id
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def process_video_background(match_id: str, video_path: str):
    """معالجة الفيديو في الخلفية"""
    try:
        # تحديث حالة المعالجة
        db_manager.update_match(match_id, {
            "processing_status": "processing",
            "processing_progress": 0.0
        })

        # إنشاء مهمة معالجة
        job_id = db_manager.create_processing_job({
            "match_id": match_id,
            "job_type": "video_analysis",
            "status": "running",
            "parameters": {"video_path": video_path}
        })

        # معالجة الفيديو
        processor = VideoProcessor({
            "output_dir": str(output_dir / match_id),
            "progress_callback": lambda progress, frame, total: update_progress(
                match_id, job_id, progress
            )
        })

        results = processor.process_video(video_path)

        # تحليل النتائج
        analyzer = MatchAnalyzer()
        match_stats = analyzer.analyze_match(results["frame_data"])

        # حفظ النتائج في قاعدة البيانات
        await save_analysis_results(match_id, match_stats, results)

        # تحديث حالة الإكمال
        db_manager.update_match(match_id, {
            "processing_status": "completed",
            "processing_progress": 100.0
        })

        db_manager.update_job_progress(job_id, 100.0, "completed")

    except Exception as e:
        # تحديث حالة الخطأ
        db_manager.update_match(match_id, {
            "processing_status": "failed",
            "processing_progress": 0.0
        })

        if 'job_id' in locals():
            db_manager.update_job_progress(job_id, 0.0, "failed")

        print(f"خطأ في معالجة الفيديو: {e}")

def update_progress(match_id: str, job_id: str, progress: float):
    """تحديث تقدم المعالجة"""
    db_manager.update_match(match_id, {"processing_progress": progress})
    db_manager.update_job_progress(job_id, progress)

async def save_analysis_results(match_id: str, match_stats, processing_results):
    """حفظ نتائج التحليل في قاعدة البيانات"""
    # حفظ إحصائيات اللاعبين
    for player_id, player_stats in match_stats.player_stats.items():
        player_data = {
            "match_id": match_id,
            "track_id": player_id,
            "total_distance": player_stats.total_distance,
            "max_speed": player_stats.max_speed,
            "average_speed": player_stats.average_speed,
            "sprint_count": player_stats.sprint_count,
            "ball_touches": player_stats.ball_touches,
            "passes_attempted": player_stats.passes_attempted,
            "passes_completed": player_stats.passes_completed,
            "pass_accuracy": player_stats.pass_accuracy,
            "shots": player_stats.shots,
            "goals": player_stats.goals,
            "tackles": player_stats.tackles,
            "fouls_committed": player_stats.fouls_committed,
            "yellow_cards": player_stats.yellow_cards,
            "red_cards": player_stats.red_cards,
            "technical_rating": player_stats.technical_rating,
            "physical_rating": player_stats.physical_rating,
            "tactical_rating": player_stats.tactical_rating,
            "mental_rating": player_stats.mental_rating,
            "overall_rating": player_stats.overall_rating,
            "heatmap_data": match_stats.heatmaps.get(player_id, []).tolist() if player_id in match_stats.heatmaps else None
        }
        db_manager.create_player(player_data)

    # حفظ الأحداث
    events_data = []
    for event in match_stats.events:
        event_data = {
            "match_id": match_id,
            "event_type": event["event_type"],
            "timestamp": event["timestamp"],
            "frame_id": event["frame_id"],
            "confidence": event["confidence"],
            "location_x": event["location"][0] if "location" in event else None,
            "location_y": event["location"][1] if "location" in event else None,
            "event_metadata": event.get("metadata", {})
        }
        events_data.append(event_data)

    if events_data:
        db_manager.create_events_batch(events_data)


# ==================== الإحصائيات والتحليلات ====================

@app.get("/matches/{match_id}/statistics", response_model=MatchStatistics)
async def get_match_statistics(match_id: str):
    """الحصول على إحصائيات المباراة"""
    try:
        match = db_manager.get_match(match_id)
        if not match:
            raise HTTPException(status_code=404, detail="المباراة غير موجودة")

        # الحصول على البيانات
        players = db_manager.get_players_by_match(match_id)
        teams = db_manager.get_teams_by_match(match_id)
        events = db_manager.get_events_by_match(match_id)

        # تجميع الإحصائيات
        player_stats = [PlayerStatistics.model_validate(player) for player in players]
        team_stats = [TeamStatistics.model_validate(team) for team in teams]

        return MatchStatistics(
            match_id=match_id,
            duration=match.duration,
            total_events=len(events),
            players=player_stats,
            teams=team_stats,
            events=[EventResponse.model_validate(event) for event in events[:100]]  # أول 100 حدث
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/matches/{match_id}/heatmap/{player_id}")
async def get_player_heatmap(match_id: str, player_id: str):
    """الحصول على الخريطة الحرارية للاعب"""
    try:
        player = db_manager.get_player_by_track_id(match_id, int(player_id))
        if not player or not player.heatmap_data:
            raise HTTPException(status_code=404, detail="الخريطة الحرارية غير متوفرة")

        return {
            "player_id": player_id,
            "heatmap": player.heatmap_data
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/matches/{match_id}/events")
async def get_match_events(
    match_id: str,
    event_type: Optional[str] = None,
    limit: int = 100,
    offset: int = 0
):
    """الحصول على أحداث المباراة"""
    try:
        events = db_manager.get_events_by_match(match_id, event_type)

        # تطبيق التصفح
        paginated_events = events[offset:offset + limit]

        return {
            "total": len(events),
            "events": [EventResponse.model_validate(event) for event in paginated_events]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ==================== تصدير البيانات ====================

@app.get("/matches/{match_id}/export/{format}")
async def export_match_data(match_id: str, format: str):
    """تصدير بيانات المباراة"""
    try:
        if format not in ["json", "csv", "pdf"]:
            raise HTTPException(status_code=400, detail="تنسيق غير مدعوم")

        match = db_manager.get_match(match_id)
        if not match:
            raise HTTPException(status_code=404, detail="المباراة غير موجودة")

        # إنشاء ملف التصدير
        export_path = await create_export_file(match_id, format)

        return FileResponse(
            path=export_path,
            filename=f"match_{match_id}.{format}",
            media_type="application/octet-stream"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def create_export_file(match_id: str, format: str) -> str:
    """إنشاء ملف التصدير"""
    # هذا تنفيذ مبسط - يمكن تحسينه
    export_dir = output_dir / "exports"
    export_dir.mkdir(exist_ok=True)

    if format == "json":
        # تصدير JSON
        players = db_manager.get_players_by_match(match_id)
        events = db_manager.get_events_by_match(match_id)

        data = {
            "match_id": match_id,
            "players": [player.__dict__ for player in players],
            "events": [event.__dict__ for event in events]
        }

        export_path = export_dir / f"match_{match_id}.json"
        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)

        return str(export_path)

    # تنسيقات أخرى...
    raise HTTPException(status_code=501, detail="التنسيق غير مدعوم حالياً")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
