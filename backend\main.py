"""
الملف الرئيسي لتشغيل نظام تحليل مباريات كرة القدم
"""

import os
import sys
import argparse
import asyncio
from pathlib import Path
import uvicorn
from typing import Optional

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.database.database import db_manager
from backend.data_processing.video_processor import VideoProcessor
from backend.analytics.match_analyzer import MatchAnalyzer


def setup_environment():
    """إعداد البيئة"""
    # إنشاء المجلدات المطلوبة
    directories = [
        "uploads",
        "output", 
        "static",
        "logs",
        "models",
        "data"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ تم إعداد البيئة بنجاح")


def init_database():
    """تهيئة قاعدة البيانات"""
    try:
        # إنشاء الجداول
        db_manager.create_tables()
        
        # طباعة إحصائيات قاعدة البيانات
        stats = db_manager.get_database_stats()
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
        print(f"📊 إحصائيات قاعدة البيانات: {stats}")
        
    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
        sys.exit(1)


def run_server(host: str = "0.0.0.0", port: int = 8000, reload: bool = False):
    """تشغيل خادم API"""
    print(f"🚀 بدء تشغيل خادم API على {host}:{port}")
    
    uvicorn.run(
        "backend.api.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )


def process_video_cli(video_path: str, output_dir: Optional[str] = None):
    """معالجة فيديو من سطر الأوامر"""
    if not Path(video_path).exists():
        print(f"❌ الفيديو غير موجود: {video_path}")
        return
    
    print(f"🎥 بدء معالجة الفيديو: {video_path}")
    
    # إعداد معالج الفيديو
    config = {
        "output_dir": output_dir or "output",
        "save_annotated_video": True,
        "save_raw_data": True,
        "save_statistics": True
    }
    
    processor = VideoProcessor(config)
    
    try:
        # معالجة الفيديو
        results = processor.process_video(video_path)
        
        # تحليل النتائج
        analyzer = MatchAnalyzer()
        match_stats = analyzer.analyze_match(results["frame_data"])
        
        # تصدير الإحصائيات
        output_path = Path(config["output_dir"])
        analyzer.export_statistics(match_stats, str(output_path))
        
        print("✅ تمت معالجة الفيديو بنجاح")
        print(f"📁 النتائج محفوظة في: {output_path}")
        
    except Exception as e:
        print(f"❌ خطأ في معالجة الفيديو: {e}")


def create_sample_match():
    """إنشاء مباراة تجريبية"""
    try:
        match_data = {
            "name": "مباراة تجريبية",
            "duration": 5400.0,  # 90 دقيقة
            "processing_status": "pending"
        }
        
        match_id = db_manager.create_match(match_data)
        print(f"✅ تم إنشاء مباراة تجريبية: {match_id}")
        
        # إنشاء فريقين
        team1_data = {
            "match_id": match_id,
            "team_number": 1,
            "name": "الفريق الأول",
            "color": "#FF0000",
            "formation": "4-3-3"
        }
        
        team2_data = {
            "match_id": match_id,
            "team_number": 2,
            "name": "الفريق الثاني", 
            "color": "#0000FF",
            "formation": "4-4-2"
        }
        
        team1_id = db_manager.create_team(team1_data)
        team2_id = db_manager.create_team(team2_data)
        
        print(f"✅ تم إنشاء الفريقين: {team1_id}, {team2_id}")
        
        return match_id
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المباراة التجريبية: {e}")


def show_system_info():
    """عرض معلومات النظام"""
    print("=" * 50)
    print("🏆 نظام Taktik لتحليل مباريات كرة القدم")
    print("=" * 50)
    
    # معلومات قاعدة البيانات
    try:
        stats = db_manager.get_database_stats()
        print("📊 إحصائيات قاعدة البيانات:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
    except Exception as e:
        print(f"❌ خطأ في الوصول لقاعدة البيانات: {e}")
    
    print()
    
    # معلومات النظام
    import torch
    print("🖥️  معلومات النظام:")
    print(f"   Python: {sys.version.split()[0]}")
    print(f"   PyTorch: {torch.__version__}")
    print(f"   CUDA متوفر: {'نعم' if torch.cuda.is_available() else 'لا'}")
    if torch.cuda.is_available():
        print(f"   عدد GPUs: {torch.cuda.device_count()}")
        print(f"   GPU الحالي: {torch.cuda.get_device_name()}")
    
    print()
    
    # مسارات المشروع
    print("📁 مسارات المشروع:")
    print(f"   المجلد الرئيسي: {project_root}")
    print(f"   مجلد التحميل: {project_root / 'uploads'}")
    print(f"   مجلد المخرجات: {project_root / 'output'}")
    print(f"   مجلد النماذج: {project_root / 'models'}")


def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(
        description="نظام Taktik لتحليل مباريات كرة القدم",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python backend/main.py server                    # تشغيل الخادم
  python backend/main.py server --port 8080        # تشغيل الخادم على منفذ مخصص
  python backend/main.py process video.mp4         # معالجة فيديو
  python backend/main.py init                      # تهيئة النظام
  python backend/main.py info                      # عرض معلومات النظام
        """
    )
    
    subparsers = parser.add_subparsers(dest="command", help="الأوامر المتاحة")
    
    # أمر تشغيل الخادم
    server_parser = subparsers.add_parser("server", help="تشغيل خادم API")
    server_parser.add_argument("--host", default="0.0.0.0", help="عنوان الخادم")
    server_parser.add_argument("--port", type=int, default=8000, help="منفذ الخادم")
    server_parser.add_argument("--reload", action="store_true", help="إعادة التحميل التلقائي")
    
    # أمر معالجة الفيديو
    process_parser = subparsers.add_parser("process", help="معالجة فيديو")
    process_parser.add_argument("video_path", help="مسار ملف الفيديو")
    process_parser.add_argument("--output", help="مجلد المخرجات")
    
    # أمر التهيئة
    init_parser = subparsers.add_parser("init", help="تهيئة النظام")
    init_parser.add_argument("--sample", action="store_true", help="إنشاء بيانات تجريبية")
    
    # أمر عرض المعلومات
    info_parser = subparsers.add_parser("info", help="عرض معلومات النظام")
    
    # أمر تنظيف قاعدة البيانات
    cleanup_parser = subparsers.add_parser("cleanup", help="تنظيف قاعدة البيانات")
    cleanup_parser.add_argument("--days", type=int, default=30, help="حذف البيانات الأقدم من (أيام)")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # إعداد البيئة
    setup_environment()
    
    if args.command == "server":
        init_database()
        run_server(args.host, args.port, args.reload)
    
    elif args.command == "process":
        init_database()
        process_video_cli(args.video_path, args.output)
    
    elif args.command == "init":
        init_database()
        if args.sample:
            create_sample_match()
        print("✅ تم تهيئة النظام بنجاح")
    
    elif args.command == "info":
        show_system_info()
    
    elif args.command == "cleanup":
        init_database()
        deleted = db_manager.cleanup_old_data(args.days)
        print(f"✅ تم تنظيف قاعدة البيانات: {deleted}")
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
