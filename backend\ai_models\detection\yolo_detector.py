"""
نموذج كشف اللاعبين والكرة باستخدام YOLOv8
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Optional
from ultralytics import YOLO
import torch

from ..base_model import BaseModel, ModelType, BoundingBox, DetectionResult


class YOLODetector(BaseModel):
    """كاشف YOLOv8 للاعبين والكرة والحكام"""
    
    # فئات الكشف
    CLASS_NAMES = {
        0: "person",      # شخص (لاعب/حكم)
        1: "ball",        # كرة
        2: "referee",     # حكم
        3: "goalkeeper",  # حارس مرمى
    }
    
    def __init__(self, model_path: Optional[str] = None, config: Optional[Dict] = None):
        default_config = {
            "confidence_threshold": 0.5,
            "iou_threshold": 0.45,
            "max_detections": 100,
            "input_size": 640,
            "device": "cuda" if torch.cuda.is_available() else "cpu"
        }
        
        if config:
            default_config.update(config)
            
        super().__init__(model_path, default_config)
        self.model = None
        
    def _get_model_type(self) -> ModelType:
        return ModelType.DETECTION
    
    def load_model(self) -> bool:
        """تحميل نموذج YOLOv8"""
        try:
            if self.model_path:
                # تحميل نموذج مخصص
                self.model = YOLO(self.model_path)
            else:
                # تحميل نموذج مدرب مسبقاً
                self.model = YOLO('yolov8n.pt')  # يمكن تغييرها إلى yolov8s.pt, yolov8m.pt, etc.
            
            # تعيين الجهاز
            self.model.to(self.config["device"])
            self.is_loaded = True
            
            print(f"تم تحميل نموذج YOLOv8 بنجاح على {self.config['device']}")
            return True
            
        except Exception as e:
            print(f"خطأ في تحميل نموذج YOLOv8: {e}")
            self.is_loaded = False
            return False
    
    def preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """معالجة مسبقة للإطار"""
        if not self.validate_frame(frame):
            raise ValueError("إطار غير صالح")
        
        # تحويل من BGR إلى RGB إذا لزم الأمر
        if len(frame.shape) == 3 and frame.shape[2] == 3:
            # YOLOv8 يتوقع RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            return frame_rgb
        
        return frame
    
    def predict(self, frame: np.ndarray, **kwargs) -> DetectionResult:
        """كشف الكيانات في الإطار"""
        if not self.is_loaded:
            raise RuntimeError("النموذج غير محمل")
        
        frame_id = kwargs.get("frame_id", 0)
        timestamp = kwargs.get("timestamp", 0.0)
        
        # معالجة مسبقة
        processed_frame = self.preprocess_frame(frame)
        
        # التنبؤ
        results = self.model(
            processed_frame,
            conf=self.config["confidence_threshold"],
            iou=self.config["iou_threshold"],
            max_det=self.config["max_detections"],
            verbose=False
        )
        
        # استخراج النتائج
        bounding_boxes = self._extract_detections(results[0], frame.shape)
        
        return DetectionResult(
            frame_id=frame_id,
            timestamp=timestamp,
            bounding_boxes=bounding_boxes,
            frame_shape=frame.shape
        )
    
    def _extract_detections(self, result, frame_shape: tuple) -> List[BoundingBox]:
        """استخراج مربعات الحدود من نتائج YOLO"""
        bounding_boxes = []
        
        if result.boxes is not None:
            boxes = result.boxes.xyxy.cpu().numpy()  # [x1, y1, x2, y2]
            confidences = result.boxes.conf.cpu().numpy()
            class_ids = result.boxes.cls.cpu().numpy().astype(int)
            
            for i, (box, conf, cls_id) in enumerate(zip(boxes, confidences, class_ids)):
                x1, y1, x2, y2 = box
                
                # التأكد من أن الإحداثيات داخل حدود الإطار
                x1 = max(0, min(x1, frame_shape[1]))
                y1 = max(0, min(y1, frame_shape[0]))
                x2 = max(0, min(x2, frame_shape[1]))
                y2 = max(0, min(y2, frame_shape[0]))
                
                # تصنيف الكيان
                class_name = self._classify_detection(cls_id, box, frame_shape)
                
                bbox = BoundingBox(
                    x1=float(x1),
                    y1=float(y1),
                    x2=float(x2),
                    y2=float(y2),
                    confidence=float(conf),
                    class_id=int(cls_id),
                    class_name=class_name
                )
                
                bounding_boxes.append(bbox)
        
        return bounding_boxes
    
    def _classify_detection(self, class_id: int, box: np.ndarray, frame_shape: tuple) -> str:
        """تصنيف الكشف بناءً على الفئة والموقع"""
        base_class = self.CLASS_NAMES.get(class_id, "unknown")
        
        if base_class == "person":
            # تحديد نوع الشخص بناءً على الموقع والحجم
            x1, y1, x2, y2 = box
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2
            
            # منطقة المرمى (تقريبية)
            frame_width = frame_shape[1]
            frame_height = frame_shape[0]
            
            # إذا كان قريباً من المرمى
            if (center_x < frame_width * 0.15 or center_x > frame_width * 0.85) and \
               (center_y > frame_height * 0.3 and center_y < frame_height * 0.7):
                return "goalkeeper"
            
            # إذا كان في المنتصف وحجم مختلف (قد يكون حكم)
            box_area = (x2 - x1) * (y2 - y1)
            if center_x > frame_width * 0.4 and center_x < frame_width * 0.6:
                return "referee"
            
            return "player"
        
        return base_class
    
    def filter_detections(self, detections: List[BoundingBox], 
                         min_area: float = 100.0,
                         max_area: float = 50000.0) -> List[BoundingBox]:
        """تصفية الكشوفات بناءً على المعايير"""
        filtered = []
        
        for detection in detections:
            area = detection.area
            
            # تصفية بناءً على المساحة
            if area < min_area or area > max_area:
                continue
            
            # تصفية بناءً على نسبة العرض إلى الارتفاع
            aspect_ratio = detection.width / detection.height
            if detection.class_name in ["player", "goalkeeper", "referee"]:
                # الأشخاص يجب أن يكونوا أطول من عرضهم
                if aspect_ratio > 1.5:
                    continue
            
            filtered.append(detection)
        
        return filtered
    
    def draw_detections(self, frame: np.ndarray, detections: List[BoundingBox]) -> np.ndarray:
        """رسم الكشوفات على الإطار"""
        result_frame = frame.copy()
        
        # ألوان مختلفة لكل فئة
        colors = {
            "player": (0, 255, 0),      # أخضر
            "goalkeeper": (255, 0, 0),   # أحمر
            "referee": (0, 0, 255),      # أزرق
            "ball": (255, 255, 0),       # أصفر
            "unknown": (128, 128, 128)   # رمادي
        }
        
        for detection in detections:
            color = colors.get(detection.class_name, colors["unknown"])
            
            # رسم المربع
            cv2.rectangle(
                result_frame,
                (int(detection.x1), int(detection.y1)),
                (int(detection.x2), int(detection.y2)),
                color,
                2
            )
            
            # رسم النص
            label = f"{detection.class_name}: {detection.confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            
            cv2.rectangle(
                result_frame,
                (int(detection.x1), int(detection.y1) - label_size[1] - 10),
                (int(detection.x1) + label_size[0], int(detection.y1)),
                color,
                -1
            )
            
            cv2.putText(
                result_frame,
                label,
                (int(detection.x1), int(detection.y1) - 5),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                (255, 255, 255),
                2
            )
        
        return result_frame
