# سكريبت PowerShell لرفع فيديو إلى نظام Taktik

param(
    [Parameter(Mandatory=$true)]
    [string]$MatchId,
    
    [Parameter(Mandatory=$true)]
    [string]$VideoPath
)

# التحقق من وجود الملف
if (-not (Test-Path $VideoPath)) {
    Write-Host "❌ الملف غير موجود: $VideoPath" -ForegroundColor Red
    exit 1
}

# معلومات الرفع
$uri = "http://localhost:8000/matches/$MatchId/upload-video"
Write-Host "🚀 رفع الفيديو..." -ForegroundColor Yellow
Write-Host "   الملف: $VideoPath" -ForegroundColor Gray
Write-Host "   المباراة: $MatchId" -ForegroundColor Gray
Write-Host "   الرابط: $uri" -ForegroundColor Gray

try {
    # قراءة الملف
    $fileBytes = [System.IO.File]::ReadAllBytes($VideoPath)
    $fileName = [System.IO.Path]::GetFileName($VideoPath)
    
    # إنشاء multipart/form-data
    $boundary = [System.Guid]::NewGuid().ToString()
    $LF = "`r`n"
    
    $bodyLines = @(
        "--$boundary",
        "Content-Disposition: form-data; name=`"file`"; filename=`"$fileName`"",
        "Content-Type: video/mp4$LF",
        [System.Text.Encoding]::GetEncoding("iso-8859-1").GetString($fileBytes),
        "--$boundary--$LF"
    ) -join $LF
    
    # رفع الملف
    $response = Invoke-RestMethod -Uri $uri -Method Post -ContentType "multipart/form-data; boundary=$boundary" -Body $bodyLines
    
    Write-Host "✅ تم رفع الفيديو بنجاح!" -ForegroundColor Green
    Write-Host "📄 الاستجابة:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3
    
} catch {
    Write-Host "❌ خطأ في رفع الفيديو:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "تفاصيل الخطأ: $responseBody" -ForegroundColor Yellow
    }
}

Write-Host "`n🔗 روابط مفيدة:" -ForegroundColor Cyan
Write-Host "   واجهة API: http://localhost:8000/docs" -ForegroundColor Gray
Write-Host "   حالة المباراة: http://localhost:8000/matches/$MatchId" -ForegroundColor Gray
