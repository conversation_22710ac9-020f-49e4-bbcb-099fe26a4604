version: '3.8'

services:
  # خدمة التطبيق الرئيسي
  taktik-app:
    build: .
    container_name: taktik-app
    ports:
      - "8000:8000"
    volumes:
      - ./uploads:/app/uploads
      - ./output:/app/output
      - ./models:/app/models
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - DATABASE_URL=*******************************************/taktik_db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - taktik-network

  # قاعدة بيانات PostgreSQL
  postgres:
    image: postgres:13
    container_name: taktik-postgres
    environment:
      - POSTGRES_DB=taktik_db
      - POSTGRES_USER=taktik
      - POSTGRES_PASSWORD=taktik123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - taktik-network

  # Redis للتخزين المؤقت والمهام
  redis:
    image: redis:6-alpine
    container_name: taktik-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - taktik-network

  # Nginx كخادم ويب عكسي
  nginx:
    image: nginx:alpine
    container_name: taktik-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./static:/var/www/static
      - ./output:/var/www/output
    depends_on:
      - taktik-app
    restart: unless-stopped
    networks:
      - taktik-network

  # خدمة معالجة المهام في الخلفية
  taktik-worker:
    build: .
    container_name: taktik-worker
    command: celery -A backend.tasks worker --loglevel=info
    volumes:
      - ./uploads:/app/uploads
      - ./output:/app/output
      - ./models:/app/models
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - DATABASE_URL=*******************************************/taktik_db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - taktik-network

  # مراقب المهام Flower
  flower:
    build: .
    container_name: taktik-flower
    command: celery -A backend.tasks flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - taktik-network

  # خدمة مراقبة النظام
  prometheus:
    image: prom/prometheus
    container_name: taktik-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - taktik-network

  # لوحة مراقبة Grafana
  grafana:
    image: grafana/grafana
    container_name: taktik-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - taktik-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  taktik-network:
    driver: bridge
