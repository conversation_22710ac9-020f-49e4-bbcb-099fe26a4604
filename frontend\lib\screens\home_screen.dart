import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/match_provider.dart';
import '../widgets/match_card.dart';
import '../widgets/app_drawer.dart';
import '../widgets/loading_widget.dart';
import '../widgets/error_widget.dart';
import 'create_match_screen.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Load matches when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(matchesProvider.notifier).loadMatches();
    });
  }

  @override
  Widget build(BuildContext context) {
    final matchesState = ref.watch(matchesProvider);
    final healthCheck = ref.watch(healthCheckProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Taktik - تحليل مباريات كرة القدم',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // Health status indicator
          healthCheck.when(
            data: (isHealthy) => Icon(
              isHealthy ? Icons.cloud_done : Icons.cloud_off,
              color: isHealthy ? Colors.green : Colors.red,
            ),
            loading: () => const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            error: (_, __) => const Icon(Icons.error, color: Colors.red),
          ),
          const SizedBox(width: 16),
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(matchesProvider.notifier).loadMatches();
              ref.refresh(healthCheckProvider);
            },
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: RefreshIndicator(
        onRefresh: () async {
          await ref.read(matchesProvider.notifier).loadMatches();
        },
        child: _buildBody(matchesState),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToCreateMatch(context),
        icon: const Icon(Icons.add),
        label: const Text('مباراة جديدة'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildBody(MatchesState state) {
    if (state.isLoading && state.matches.isEmpty) {
      return const LoadingWidget(message: 'جاري تحميل المباريات...');
    }

    if (state.error != null && state.matches.isEmpty) {
      return CustomErrorWidget(
        message: state.error!,
        onRetry: () => ref.read(matchesProvider.notifier).loadMatches(),
      );
    }

    if (state.matches.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // Error banner if there's an error but we have cached data
        if (state.error != null)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            color: Colors.red.shade100,
            child: Row(
              children: [
                Icon(Icons.error, color: Colors.red.shade700),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'خطأ في التحديث: ${state.error}',
                    style: TextStyle(color: Colors.red.shade700),
                  ),
                ),
                TextButton(
                  onPressed: () => ref.read(matchesProvider.notifier).clearError(),
                  child: const Text('إخفاء'),
                ),
              ],
            ),
          ),
        
        // Matches list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: state.matches.length,
            itemBuilder: (context, index) {
              final match = state.matches[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: MatchCard(
                  match: match,
                  onTap: () => _navigateToMatchDetails(context, match.id),
                  onDelete: () => _deleteMatch(match),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            MdiIcons.soccerField,
            size: 120,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد مباريات',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey.shade600,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'ابدأ بإنشاء مباراة جديدة لتحليلها',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () => _navigateToCreateMatch(context),
            icon: const Icon(Icons.add),
            label: const Text('إنشاء مباراة جديدة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToCreateMatch(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CreateMatchScreen(),
      ),
    );
  }

  void _navigateToMatchDetails(BuildContext context, String matchId) {
    Navigator.of(context).pushNamed('/match/$matchId');
  }

  void _deleteMatch(Match match) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المباراة'),
        content: Text('هل أنت متأكد من حذف مباراة "${match.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(matchesProvider.notifier).deleteMatch(match.id);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف مباراة "${match.name}"'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
