import 'package:json_annotation/json_annotation.dart';

part 'player.g.dart';

@JsonSerializable()
class Player {
  final String id;
  @Json<PERSON><PERSON>(name: 'track_id')
  final int trackId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'jersey_number')
  final int? jerseyNumber;
  final String? name;
  final String? position;
  
  // إحصائيات الحركة
  @JsonKey(name: 'total_distance')
  final double totalDistance;
  @<PERSON>sonKey(name: 'max_speed')
  final double maxSpeed;
  @<PERSON>sonKey(name: 'average_speed')
  final double averageSpeed;
  @<PERSON>sonKey(name: 'sprint_count')
  final int sprintCount;
  
  // إحصائيات الكرة
  @JsonKey(name: 'ball_touches')
  final int ballTouches;
  @JsonKey(name: 'passes_attempted')
  final int passesAttempted;
  @<PERSON>sonKey(name: 'passes_completed')
  final int passesCompleted;
  @JsonKey(name: 'pass_accuracy')
  final double passAccuracy;
  final int shots;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'shots_on_target')
  final int shotsOnTarget;
  final int goals;
  final int assists;
  
  // إحصائيات دفاعية
  final int tackles;
  final int interceptions;
  final int clearances;
  final int blocks;
  
  // إحصائيات هجومية
  final int dribbles;
  final int crosses;
  final int headers;
  
  // إحصائيات انضباطية
  @JsonKey(name: 'fouls_committed')
  final int foulsCommitted;
  @JsonKey(name: 'fouls_received')
  final int foulsReceived;
  @JsonKey(name: 'yellow_cards')
  final int yellowCards;
  @JsonKey(name: 'red_cards')
  final int redCards;
  
  // تقييمات الأداء
  @JsonKey(name: 'technical_rating')
  final double technicalRating;
  @JsonKey(name: 'physical_rating')
  final double physicalRating;
  @JsonKey(name: 'tactical_rating')
  final double tacticalRating;
  @JsonKey(name: 'mental_rating')
  final double mentalRating;
  @JsonKey(name: 'overall_rating')
  final double overallRating;

  Player({
    required this.id,
    required this.trackId,
    this.jerseyNumber,
    this.name,
    this.position,
    required this.totalDistance,
    required this.maxSpeed,
    required this.averageSpeed,
    required this.sprintCount,
    required this.ballTouches,
    required this.passesAttempted,
    required this.passesCompleted,
    required this.passAccuracy,
    required this.shots,
    required this.shotsOnTarget,
    required this.goals,
    required this.assists,
    required this.tackles,
    required this.interceptions,
    required this.clearances,
    required this.blocks,
    required this.dribbles,
    required this.crosses,
    required this.headers,
    required this.foulsCommitted,
    required this.foulsReceived,
    required this.yellowCards,
    required this.redCards,
    required this.technicalRating,
    required this.physicalRating,
    required this.tacticalRating,
    required this.mentalRating,
    required this.overallRating,
  });

  factory Player.fromJson(Map<String, dynamic> json) => _$PlayerFromJson(json);
  Map<String, dynamic> toJson() => _$PlayerToJson(this);

  // Helper methods
  String get displayName => name ?? 'لاعب #${jerseyNumber ?? trackId}';
  
  String get positionText {
    switch (position) {
      case 'goalkeeper':
        return 'حارس مرمى';
      case 'defender':
        return 'مدافع';
      case 'midfielder':
        return 'وسط ميدان';
      case 'forward':
        return 'مهاجم';
      default:
        return 'غير محدد';
    }
  }

  String get totalDistanceKm => '${(totalDistance / 1000).toStringAsFixed(1)} كم';
  String get maxSpeedKmh => '${(maxSpeed * 3.6).toStringAsFixed(1)} كم/س';
  String get averageSpeedKmh => '${(averageSpeed * 3.6).toStringAsFixed(1)} كم/س';

  // Rating colors
  Color get overallRatingColor {
    if (overallRating >= 80) return Colors.green;
    if (overallRating >= 60) return Colors.orange;
    return Colors.red;
  }

  // Performance indicators
  bool get isTopPerformer => overallRating >= 80;
  bool get needsImprovement => overallRating < 60;
}
