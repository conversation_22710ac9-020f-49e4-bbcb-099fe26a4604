// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Match _$MatchFromJson(Map<String, dynamic> json) => Match(
      id: json['id'] as String,
      name: json['name'] as String,
      date:
          json['date'] == null ? null : DateTime.parse(json['date'] as String),
      duration: (json['duration'] as num?)?.toDouble(),
      videoPath: json['video_path'] as String?,
      videoFps: (json['video_fps'] as num?)?.toDouble(),
      videoWidth: (json['video_width'] as num?)?.toInt(),
      videoHeight: (json['video_height'] as num?)?.toInt(),
      processingStatus: json['processing_status'] as String,
      processingProgress: (json['processing_progress'] as num).toDouble(),
      totalFrames: (json['total_frames'] as num?)?.toInt(),
      processedFrames: (json['processed_frames'] as num?)?.toInt(),
      totalEvents: (json['total_events'] as num?)?.toInt(),
      ballInPlayTime: (json['ball_in_play_time'] as num?)?.toDouble(),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$MatchToJson(Match instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'date': instance.date?.toIso8601String(),
      'duration': instance.duration,
      'video_path': instance.videoPath,
      'video_fps': instance.videoFps,
      'video_width': instance.videoWidth,
      'video_height': instance.videoHeight,
      'processing_status': instance.processingStatus,
      'processing_progress': instance.processingProgress,
      'total_frames': instance.totalFrames,
      'processed_frames': instance.processedFrames,
      'total_events': instance.totalEvents,
      'ball_in_play_time': instance.ballInPlayTime,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };
