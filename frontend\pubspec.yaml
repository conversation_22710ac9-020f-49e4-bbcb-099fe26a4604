name: taktik_app
description: تطبيق Taktik لتحليل مباريات كرة القدم
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI Components
  cupertino_icons: ^1.0.6
  material_design_icons_flutter: ^7.0.7296
  
  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # HTTP & API
  http: ^1.1.2
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # File Handling
  file_picker: ^6.1.1
  path_provider: ^2.1.2
  permission_handler: ^11.2.0
  
  # Video Player
  video_player: ^2.8.2
  chewie: ^1.7.4
  
  # Charts & Visualization
  fl_chart: ^0.66.0
  syncfusion_flutter_charts: ^24.1.41
  
  # Navigation
  go_router: ^12.1.3
  
  # Utils
  intl: ^0.19.0
  shared_preferences: ^2.2.2
  cached_network_image: ^3.3.1
  shimmer: ^3.0.0
  
  # Animation
  lottie: ^2.7.0
  animations: ^2.0.11
  
  # PDF & Export
  pdf: ^3.10.7
  printing: ^5.12.0
  
  # Responsive Design
  responsive_framework: ^1.1.1
  flutter_screenutil: ^5.9.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1
  
  # Linting
  flutter_lints: ^3.0.1

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
  
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600

