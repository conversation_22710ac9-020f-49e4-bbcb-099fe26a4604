"""
النموذج الأساسي لجميع نماذج الذكاء الاصطناعي في النظام
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple
import numpy as np
import cv2
from dataclasses import dataclass
from enum import Enum


class ModelType(Enum):
    """أنواع النماذج المختلفة"""
    DETECTION = "detection"
    TRACKING = "tracking"
    POSE_ESTIMATION = "pose_estimation"
    EVENT_DETECTION = "event_detection"
    FORMATION_ANALYSIS = "formation_analysis"


@dataclass
class BoundingBox:
    """مربع الحدود للكيانات المكتشفة"""
    x1: float
    y1: float
    x2: float
    y2: float
    confidence: float
    class_id: int
    class_name: str
    
    @property
    def center(self) -> Tuple[float, float]:
        """مركز المربع"""
        return ((self.x1 + self.x2) / 2, (self.y1 + self.y2) / 2)
    
    @property
    def width(self) -> float:
        """عرض المربع"""
        return self.x2 - self.x1
    
    @property
    def height(self) -> float:
        """ارتفاع المربع"""
        return self.y2 - self.y1
    
    @property
    def area(self) -> float:
        """مساحة المربع"""
        return self.width * self.height


@dataclass
class DetectionResult:
    """نتيجة الكشف"""
    frame_id: int
    timestamp: float
    bounding_boxes: List[BoundingBox]
    frame_shape: Tuple[int, int, int]  # (height, width, channels)


@dataclass
class TrackingResult:
    """نتيجة التتبع"""
    frame_id: int
    timestamp: float
    tracks: Dict[int, BoundingBox]  # track_id -> bounding_box
    frame_shape: Tuple[int, int, int]


@dataclass
class PoseKeypoint:
    """نقطة مفصلية في الجسم"""
    x: float
    y: float
    confidence: float
    visible: bool


@dataclass
class PoseResult:
    """نتيجة تحليل الوضعية"""
    frame_id: int
    timestamp: float
    person_id: int
    keypoints: Dict[str, PoseKeypoint]  # joint_name -> keypoint
    bbox: BoundingBox


@dataclass
class EventResult:
    """نتيجة تصنيف الحدث"""
    frame_id: int
    timestamp: float
    event_type: str
    confidence: float
    participants: List[int]  # player IDs involved
    location: Tuple[float, float]  # (x, y) on field
    metadata: Dict[str, Any]


class BaseModel(ABC):
    """النموذج الأساسي لجميع نماذج الذكاء الاصطناعي"""
    
    def __init__(self, model_path: Optional[str] = None, config: Optional[Dict] = None):
        self.model_path = model_path
        self.config = config or {}
        self.is_loaded = False
        self.model_type = self._get_model_type()
        
    @abstractmethod
    def _get_model_type(self) -> ModelType:
        """إرجاع نوع النموذج"""
        pass
    
    @abstractmethod
    def load_model(self) -> bool:
        """تحميل النموذج"""
        pass
    
    @abstractmethod
    def predict(self, frame: np.ndarray, **kwargs) -> Any:
        """التنبؤ على إطار واحد"""
        pass
    
    def preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """معالجة مسبقة للإطار"""
        return frame
    
    def postprocess_results(self, results: Any) -> Any:
        """معالجة لاحقة للنتائج"""
        return results
    
    def validate_frame(self, frame: np.ndarray) -> bool:
        """التحقق من صحة الإطار"""
        if frame is None or frame.size == 0:
            return False
        if len(frame.shape) != 3:
            return False
        return True
    
    def get_model_info(self) -> Dict[str, Any]:
        """معلومات النموذج"""
        return {
            "model_type": self.model_type.value,
            "model_path": self.model_path,
            "is_loaded": self.is_loaded,
            "config": self.config
        }


class ModelManager:
    """مدير النماذج"""
    
    def __init__(self):
        self.models: Dict[str, BaseModel] = {}
        self.active_models: Dict[ModelType, str] = {}
    
    def register_model(self, name: str, model: BaseModel) -> bool:
        """تسجيل نموذج جديد"""
        try:
            if model.load_model():
                self.models[name] = model
                return True
            return False
        except Exception as e:
            print(f"خطأ في تسجيل النموذج {name}: {e}")
            return False
    
    def get_model(self, name: str) -> Optional[BaseModel]:
        """الحصول على نموذج بالاسم"""
        return self.models.get(name)
    
    def set_active_model(self, model_type: ModelType, model_name: str) -> bool:
        """تعيين النموذج النشط لنوع معين"""
        if model_name in self.models:
            model = self.models[model_name]
            if model.model_type == model_type:
                self.active_models[model_type] = model_name
                return True
        return False
    
    def get_active_model(self, model_type: ModelType) -> Optional[BaseModel]:
        """الحصول على النموذج النشط لنوع معين"""
        model_name = self.active_models.get(model_type)
        if model_name:
            return self.models.get(model_name)
        return None
    
    def list_models(self) -> Dict[str, Dict[str, Any]]:
        """قائمة جميع النماذج المسجلة"""
        return {name: model.get_model_info() for name, model in self.models.items()}


# مثيل عام لمدير النماذج
model_manager = ModelManager()
