# 🚀 دليل البدء السريع - نظام Taktik

## ⚡ التشغيل السريع (5 دقائق)

### 1. التحميل والإعداد

```bash
# تحميل المشروع
git clone https://github.com/your-username/taktik.git
cd taktik

# إعداد النظام بالكامل (تلقائي)
python run.py setup

# تشغيل الخادم
python run.py server
```

### 2. الوصول للنظام

- 🌐 **التطبيق**: http://localhost:8000
- 📚 **API Docs**: http://localhost:8000/docs
- ✅ **Health Check**: http://localhost:8000/health

### 3. اختبار سريع

```bash
# معالجة فيديو تجريبي
python run.py process path/to/your/video.mp4

# تشغيل الاختبارات
python run.py test
```

---

## 🐳 التشغيل باستخدام Docker

```bash
# بناء وتشغيل جميع الخدمات
python run.py docker-build
python run.py docker-run

# أو مباشرة
docker-compose up -d
```

**الخدمات المتاحة:**
- 🌐 التطبيق: http://localhost
- 📊 Grafana: http://localhost:3000 (admin/admin123)
- 🌸 Flower: http://localhost:5555
- 📈 Prometheus: http://localhost:9090

---

## 📱 استخدام API

### إنشاء مباراة جديدة

```bash
curl -X POST "http://localhost:8000/matches/" \
  -H "Content-Type: application/json" \
  -d '{"name": "مباراة تجريبية"}'
```

### رفع فيديو

```bash
curl -X POST "http://localhost:8000/matches/{match_id}/upload-video" \
  -F "file=@video.mp4"
```

### الحصول على النتائج

```bash
# حالة المعالجة
curl "http://localhost:8000/matches/{match_id}"

# الإحصائيات
curl "http://localhost:8000/matches/{match_id}/statistics"

# الأحداث
curl "http://localhost:8000/matches/{match_id}/events"
```

---

## 🔧 أوامر التطوير

### إدارة النظام

```bash
# عرض معلومات النظام
python run.py info

# تنظيف قاعدة البيانات
python backend/main.py cleanup --days 30

# إعادة تهيئة قاعدة البيانات
python backend/main.py init --sample
```

### الاختبار والتطوير

```bash
# تشغيل الاختبارات
python run.py test

# تشغيل الخادم مع إعادة التحميل التلقائي
python run.py server --reload

# معالجة فيديو مع مخرجات مخصصة
python run.py process video.mp4 --output custom_output/
```

### Docker

```bash
# بناء الصورة
python run.py docker-build

# تشغيل الخدمات
python run.py docker-run

# إيقاف الخدمات
python run.py docker-stop

# عرض السجلات
docker-compose logs -f taktik-app
```

---

## 🎯 أمثلة عملية

### مثال 1: تحليل مباراة كاملة

```python
import requests
import time

# إنشاء مباراة
response = requests.post("http://localhost:8000/matches/", 
                        json={"name": "الأهلي ضد الزمالك"})
match_id = response.json()["id"]
print(f"تم إنشاء المباراة: {match_id}")

# رفع الفيديو
with open("match_video.mp4", "rb") as f:
    files = {"file": f}
    response = requests.post(f"http://localhost:8000/matches/{match_id}/upload-video", 
                           files=files)
print("تم رفع الفيديو، بدأت المعالجة...")

# متابعة التقدم
while True:
    response = requests.get(f"http://localhost:8000/matches/{match_id}")
    match_data = response.json()
    
    status = match_data["processing_status"]
    progress = match_data["processing_progress"]
    
    print(f"الحالة: {status}, التقدم: {progress:.1f}%")
    
    if status == "completed":
        print("تمت المعالجة بنجاح!")
        break
    elif status == "failed":
        print("فشلت المعالجة!")
        break
    
    time.sleep(10)

# الحصول على النتائج
stats = requests.get(f"http://localhost:8000/matches/{match_id}/statistics")
print("الإحصائيات:", stats.json())
```

### مثال 2: تصدير البيانات

```python
import requests

match_id = "your-match-id"

# تصدير JSON
response = requests.get(f"http://localhost:8000/matches/{match_id}/export/json")
with open("match_data.json", "wb") as f:
    f.write(response.content)

# تصدير CSV
response = requests.get(f"http://localhost:8000/matches/{match_id}/export/csv")
with open("match_data.csv", "wb") as f:
    f.write(response.content)
```

---

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة

**1. خطأ في تحميل النماذج**
```bash
# تحقق من توفر CUDA
python -c "import torch; print(torch.cuda.is_available())"

# تحقق من مساحة القرص
df -h

# تحقق من الذاكرة
free -h
```

**2. مشاكل في معالجة الفيديو**
```bash
# تحقق من FFmpeg
ffmpeg -version

# تحقق من صيغة الفيديو
ffprobe video.mp4
```

**3. مشاكل في قاعدة البيانات**
```bash
# إعادة تهيئة قاعدة البيانات
rm taktik.db
python backend/main.py init
```

### السجلات

```bash
# عرض سجلات التطبيق
tail -f logs/app.log

# عرض سجلات الأخطاء
tail -f logs/error.log

# سجلات Docker
docker-compose logs -f taktik-app
```

---

## 📊 مراقبة الأداء

### Grafana Dashboard

1. افتح http://localhost:3000
2. تسجيل الدخول: admin/admin123
3. استيراد dashboard من `grafana_dashboard.json`

### Prometheus Metrics

- **معدل المعالجة**: `taktik_processing_rate`
- **استخدام الذاكرة**: `taktik_memory_usage`
- **وقت الاستجابة**: `taktik_response_time`

---

## 🔗 روابط مفيدة

- 📚 [التوثيق الكامل](docs/)
- 🐛 [الإبلاغ عن مشاكل](https://github.com/your-username/taktik/issues)
- 💬 [المناقشات](https://github.com/your-username/taktik/discussions)
- 📧 [التواصل](mailto:<EMAIL>)

---

## 🎉 خطوات تالية

1. **جرب الأمثلة**: ابدأ بمعالجة فيديو قصير
2. **استكشف API**: راجع http://localhost:8000/docs
3. **طور ميزات جديدة**: أضف نماذج AI مخصصة
4. **شارك النتائج**: صدر البيانات وشاركها

---

**💡 نصيحة**: للحصول على أفضل أداء، استخدم GPU وتأكد من توفر ذاكرة كافية (8GB+ RAM موصى بها).
