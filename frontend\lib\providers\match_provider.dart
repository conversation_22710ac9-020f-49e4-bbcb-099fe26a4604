import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/match.dart';
import '../models/player.dart';
import '../services/api_service.dart';

// API Client Provider
final apiClientProvider = Provider<ApiClient>((ref) => ApiClient());

// Matches State
class MatchesState {
  final List<Match> matches;
  final bool isLoading;
  final String? error;

  MatchesState({
    this.matches = const [],
    this.isLoading = false,
    this.error,
  });

  MatchesState copyWith({
    List<Match>? matches,
    bool? isLoading,
    String? error,
  }) {
    return MatchesState(
      matches: matches ?? this.matches,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Matches Provider
class MatchesNotifier extends StateNotifier<MatchesState> {
  final ApiClient _apiClient;

  MatchesNotifier(this._apiClient) : super(MatchesState());

  Future<void> loadMatches() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final matches = await _apiClient.getMatches();
      state = state.copyWith(
        matches: matches,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<Match?> createMatch({
    required String name,
    DateTime? date,
    double? duration,
  }) async {
    try {
      final match = await _apiClient.createMatch(
        name: name,
        date: date,
        duration: duration,
      );
      
      // Add to current list
      state = state.copyWith(
        matches: [...state.matches, match],
      );
      
      return match;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return null;
    }
  }

  Future<void> deleteMatch(String id) async {
    try {
      await _apiClient.api.deleteMatch(id);
      
      // Remove from current list
      state = state.copyWith(
        matches: state.matches.where((m) => m.id != id).toList(),
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> refreshMatch(String id) async {
    try {
      final updatedMatch = await _apiClient.getMatch(id);
      
      // Update in current list
      final updatedMatches = state.matches.map((match) {
        return match.id == id ? updatedMatch : match;
      }).toList();
      
      state = state.copyWith(matches: updatedMatches);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

final matchesProvider = StateNotifierProvider<MatchesNotifier, MatchesState>((ref) {
  final apiClient = ref.watch(apiClientProvider);
  return MatchesNotifier(apiClient);
});

// Single Match Provider
final matchProvider = FutureProvider.family<Match, String>((ref, id) async {
  final apiClient = ref.watch(apiClientProvider);
  return apiClient.getMatch(id);
});

// Match Statistics Provider
final matchStatisticsProvider = FutureProvider.family<Map<String, dynamic>, String>((ref, id) async {
  final apiClient = ref.watch(apiClientProvider);
  return apiClient.getMatchStatistics(id);
});

// Match Players Provider
final matchPlayersProvider = FutureProvider.family<List<Player>, String>((ref, id) async {
  final apiClient = ref.watch(apiClientProvider);
  return apiClient.getMatchPlayers(id);
});

// Video Upload State
class VideoUploadState {
  final bool isUploading;
  final double progress;
  final String? error;
  final bool isCompleted;

  VideoUploadState({
    this.isUploading = false,
    this.progress = 0.0,
    this.error,
    this.isCompleted = false,
  });

  VideoUploadState copyWith({
    bool? isUploading,
    double? progress,
    String? error,
    bool? isCompleted,
  }) {
    return VideoUploadState(
      isUploading: isUploading ?? this.isUploading,
      progress: progress ?? this.progress,
      error: error,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}

// Video Upload Provider
class VideoUploadNotifier extends StateNotifier<VideoUploadState> {
  final ApiClient _apiClient;

  VideoUploadNotifier(this._apiClient) : super(VideoUploadState());

  Future<bool> uploadVideo(String matchId, File videoFile) async {
    state = state.copyWith(
      isUploading: true,
      progress: 0.0,
      error: null,
      isCompleted: false,
    );

    try {
      // Simulate progress updates
      for (int i = 1; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        state = state.copyWith(progress: i * 0.1);
      }

      final result = await _apiClient.uploadVideo(matchId, videoFile);
      
      state = state.copyWith(
        isUploading: false,
        progress: 1.0,
        isCompleted: true,
      );

      return true;
    } catch (e) {
      state = state.copyWith(
        isUploading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  void reset() {
    state = VideoUploadState();
  }
}

final videoUploadProvider = StateNotifierProvider<VideoUploadNotifier, VideoUploadState>((ref) {
  final apiClient = ref.watch(apiClientProvider);
  return VideoUploadNotifier(apiClient);
});

// Health Check Provider
final healthCheckProvider = FutureProvider<bool>((ref) async {
  final apiClient = ref.watch(apiClientProvider);
  return apiClient.checkHealth();
});
