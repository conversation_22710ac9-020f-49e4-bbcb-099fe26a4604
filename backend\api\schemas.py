"""
مخططات البيانات لواجهة برمجة التطبيقات
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class ProcessingStatus(str, Enum):
    """حالات المعالجة"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class EventType(str, Enum):
    """أنواع الأحداث"""
    PASS = "pass"
    SHOT = "shot"
    GOAL = "goal"
    SAVE = "save"
    TACKLE = "tackle"
    FOUL = "foul"
    CORNER = "corner"
    THROW_IN = "throw_in"
    FREE_KICK = "free_kick"
    PENALTY = "penalty"
    OFFSIDE = "offside"
    YELLOW_CARD = "yellow_card"
    RED_CARD = "red_card"
    SUBSTITUTION = "substitution"
    DRIBBLE = "dribble"
    CROSS = "cross"
    HEADER = "header"
    CLEARANCE = "clearance"
    INTERCEPTION = "interception"


class PlayerPosition(str, Enum):
    """مراكز اللاعبين"""
    GOALKEEPER = "goalkeeper"
    DEFENDER = "defender"
    MIDFIELDER = "midfielder"
    FORWARD = "forward"


# ==================== مخططات المباراة ====================

class MatchCreate(BaseModel):
    """إنشاء مباراة جديدة"""
    name: str = Field(..., description="اسم المباراة")
    date: Optional[datetime] = Field(None, description="تاريخ المباراة")
    duration: Optional[float] = Field(None, description="مدة المباراة بالثواني")
    
    class Config:
        schema_extra = {
            "example": {
                "name": "الأهلي ضد الزمالك",
                "date": "2024-01-15T20:00:00",
                "duration": 5400
            }
        }


class MatchResponse(BaseModel):
    """استجابة المباراة"""
    id: str
    name: str
    date: Optional[datetime]
    duration: Optional[float]
    video_path: Optional[str]
    video_fps: Optional[float]
    video_width: Optional[int]
    video_height: Optional[int]
    processing_status: ProcessingStatus
    processing_progress: float
    total_frames: Optional[int]
    processed_frames: Optional[int]
    total_events: Optional[int]
    ball_in_play_time: Optional[float]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


# ==================== مخططات الفريق ====================

class TeamCreate(BaseModel):
    """إنشاء فريق جديد"""
    match_id: str
    team_number: int = Field(..., ge=1, le=2, description="رقم الفريق (1 أو 2)")
    name: Optional[str] = Field(None, description="اسم الفريق")
    color: Optional[str] = Field(None, description="لون الفريق (hex)")
    formation: Optional[str] = Field(None, description="التشكيل التكتيكي")


class TeamStatistics(BaseModel):
    """إحصائيات الفريق"""
    id: str
    team_number: int
    name: Optional[str]
    possession_percentage: float
    total_passes: int
    pass_accuracy: float
    shots: int
    shots_on_target: int
    goals: int
    corners: int
    fouls: int
    yellow_cards: int
    red_cards: int
    formation: Optional[str]
    
    class Config:
        orm_mode = True


# ==================== مخططات اللاعب ====================

class PlayerCreate(BaseModel):
    """إنشاء لاعب جديد"""
    match_id: str
    team_id: Optional[str]
    track_id: int
    jersey_number: Optional[int]
    name: Optional[str]
    position: Optional[PlayerPosition]


class PlayerStatistics(BaseModel):
    """إحصائيات اللاعب"""
    id: str
    track_id: int
    jersey_number: Optional[int]
    name: Optional[str]
    position: Optional[str]
    
    # إحصائيات الحركة
    total_distance: float = Field(..., description="المسافة الإجمالية بالمتر")
    max_speed: float = Field(..., description="أقصى سرعة م/ث")
    average_speed: float = Field(..., description="متوسط السرعة م/ث")
    sprint_count: int = Field(..., description="عدد مرات الجري السريع")
    
    # إحصائيات الكرة
    ball_touches: int = Field(..., description="لمسات الكرة")
    passes_attempted: int = Field(..., description="التمريرات المحاولة")
    passes_completed: int = Field(..., description="التمريرات المكتملة")
    pass_accuracy: float = Field(..., description="دقة التمرير %")
    shots: int = Field(..., description="التسديدات")
    shots_on_target: int = Field(..., description="التسديدات على المرمى")
    goals: int = Field(..., description="الأهداف")
    assists: int = Field(..., description="التمريرات الحاسمة")
    
    # إحصائيات دفاعية
    tackles: int = Field(..., description="المقاطعات")
    interceptions: int = Field(..., description="الاعتراضات")
    clearances: int = Field(..., description="الإبعادات")
    blocks: int = Field(..., description="الصدات")
    
    # إحصائيات هجومية
    dribbles: int = Field(..., description="المراوغات")
    crosses: int = Field(..., description="العرضيات")
    headers: int = Field(..., description="الضربات الرأسية")
    
    # إحصائيات انضباطية
    fouls_committed: int = Field(..., description="الأخطاء المرتكبة")
    fouls_received: int = Field(..., description="الأخطاء المستلمة")
    yellow_cards: int = Field(..., description="البطاقات الصفراء")
    red_cards: int = Field(..., description="البطاقات الحمراء")
    
    # تقييمات الأداء
    technical_rating: float = Field(..., description="التقييم الفني")
    physical_rating: float = Field(..., description="التقييم البدني")
    tactical_rating: float = Field(..., description="التقييم التكتيكي")
    mental_rating: float = Field(..., description="التقييم النفسي")
    overall_rating: float = Field(..., description="التقييم الإجمالي")
    
    class Config:
        orm_mode = True


# ==================== مخططات الأحداث ====================

class EventCreate(BaseModel):
    """إنشاء حدث جديد"""
    match_id: str
    player_id: Optional[str]
    event_type: EventType
    timestamp: float = Field(..., description="الوقت بالثواني")
    frame_id: int = Field(..., description="رقم الإطار")
    confidence: float = Field(1.0, ge=0.0, le=1.0, description="مستوى الثقة")
    location_x: Optional[float] = Field(None, description="الموقع الأفقي")
    location_y: Optional[float] = Field(None, description="الموقع العمودي")
    successful: bool = Field(True, description="هل الحدث ناجح؟")
    description: Optional[str] = Field(None, description="وصف الحدث")
    metadata: Optional[Dict[str, Any]] = Field(None, description="بيانات إضافية")


class EventResponse(BaseModel):
    """استجابة الحدث"""
    id: str
    match_id: str
    player_id: Optional[str]
    event_type: str
    timestamp: float
    frame_id: int
    confidence: float
    location_x: Optional[float]
    location_y: Optional[float]
    successful: bool
    description: Optional[str]
    metadata: Optional[Dict[str, Any]]
    created_at: datetime
    
    class Config:
        orm_mode = True


# ==================== مخططات التحليل ====================

class MatchStatistics(BaseModel):
    """إحصائيات المباراة الكاملة"""
    match_id: str
    duration: Optional[float]
    total_events: int
    players: List[PlayerStatistics]
    teams: List[TeamStatistics]
    events: List[EventResponse]


class HeatmapData(BaseModel):
    """بيانات الخريطة الحرارية"""
    player_id: str
    heatmap: List[List[float]] = Field(..., description="مصفوفة الخريطة الحرارية")
    width: int = Field(..., description="عرض الخريطة")
    height: int = Field(..., description="ارتفاع الخريطة")


class FormationData(BaseModel):
    """بيانات التشكيل التكتيكي"""
    formation_name: str = Field(..., description="اسم التشكيل (مثل 4-3-3)")
    start_time: float = Field(..., description="وقت البداية بالثواني")
    end_time: float = Field(..., description="وقت النهاية بالثواني")
    player_positions: Dict[str, Dict[str, Any]] = Field(..., description="مواقع اللاعبين")
    effectiveness_score: float = Field(..., description="تقييم فعالية التشكيل")


# ==================== مخططات التصدير ====================

class ExportRequest(BaseModel):
    """طلب تصدير البيانات"""
    format: str = Field(..., description="تنسيق التصدير (json, csv, pdf)")
    include_events: bool = Field(True, description="تضمين الأحداث")
    include_statistics: bool = Field(True, description="تضمين الإحصائيات")
    include_heatmaps: bool = Field(False, description="تضمين الخرائط الحرارية")


class ExportResponse(BaseModel):
    """استجابة التصدير"""
    download_url: str = Field(..., description="رابط التحميل")
    file_size: int = Field(..., description="حجم الملف بالبايت")
    expires_at: datetime = Field(..., description="تاريخ انتهاء الصلاحية")


# ==================== مخططات المعالجة ====================

class ProcessingJobResponse(BaseModel):
    """استجابة مهمة المعالجة"""
    id: str
    match_id: Optional[str]
    job_type: str
    status: str
    progress: float
    error_message: Optional[str]
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    created_at: datetime
    
    class Config:
        orm_mode = True


class VideoUploadResponse(BaseModel):
    """استجابة تحميل الفيديو"""
    message: str
    video_path: str
    match_id: str
    job_id: Optional[str] = None


# ==================== مخططات الإعدادات ====================

class ProcessingConfig(BaseModel):
    """إعدادات المعالجة"""
    frame_skip: int = Field(1, ge=1, description="تخطي الإطارات")
    max_frames: Optional[int] = Field(None, description="حد أقصى للإطارات")
    save_annotated_video: bool = Field(True, description="حفظ الفيديو المُحلل")
    save_raw_data: bool = Field(True, description="حفظ البيانات الخام")
    
    # إعدادات النماذج
    detector_confidence: float = Field(0.5, ge=0.0, le=1.0)
    tracker_max_age: int = Field(30, ge=1)
    pose_min_confidence: float = Field(0.5, ge=0.0, le=1.0)
    event_confidence: float = Field(0.6, ge=0.0, le=1.0)


# ==================== مخططات الاستجابة العامة ====================

class SuccessResponse(BaseModel):
    """استجابة نجاح عامة"""
    message: str
    data: Optional[Dict[str, Any]] = None


class ErrorResponse(BaseModel):
    """استجابة خطأ"""
    error: str
    details: Optional[str] = None
    code: Optional[str] = None


class PaginatedResponse(BaseModel):
    """استجابة مُصفحة"""
    total: int
    page: int
    per_page: int
    pages: int
    data: List[Any]


# ==================== مخططات الإحصائيات ====================

class DatabaseStats(BaseModel):
    """إحصائيات قاعدة البيانات"""
    matches: int
    teams: int
    players: int
    events: int
    frame_data: int
    formations: int
    analyses: int
    processing_jobs: int


class SystemHealth(BaseModel):
    """صحة النظام"""
    status: str
    database: str
    stats: DatabaseStats
    uptime: Optional[float] = None
    memory_usage: Optional[float] = None
    cpu_usage: Optional[float] = None
