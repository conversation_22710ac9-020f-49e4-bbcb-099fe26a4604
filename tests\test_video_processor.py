"""
اختبارات معالج الفيديو
"""

import pytest
import numpy as np
import cv2
from pathlib import Path
import tempfile
import os

from backend.data_processing.video_processor import VideoProcessor
from backend.ai_models.detection.yolo_detector import YOLODetector
from backend.ai_models.tracking.deep_sort_tracker import DeepSORTTracker


class TestVideoProcessor:
    """اختبارات معالج الفيديو"""
    
    @pytest.fixture
    def sample_video(self):
        """إنشاء فيديو تجريبي للاختبار"""
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as f:
            video_path = f.name
        
        # إنشاء فيديو تجريبي
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(video_path, fourcc, 30.0, (640, 480))
        
        # إنشاء 30 إطار تجريبي
        for i in range(30):
            frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            out.write(frame)
        
        out.release()
        
        yield video_path
        
        # تنظيف
        if os.path.exists(video_path):
            os.unlink(video_path)
    
    @pytest.fixture
    def video_processor(self):
        """إنشاء معالج فيديو للاختبار"""
        config = {
            "output_dir": tempfile.mkdtemp(),
            "save_annotated_video": False,  # لتسريع الاختبار
            "save_raw_data": True,
            "max_frames": 10  # معالجة 10 إطارات فقط
        }
        return VideoProcessor(config)
    
    def test_video_processor_initialization(self, video_processor):
        """اختبار تهيئة معالج الفيديو"""
        assert video_processor is not None
        assert video_processor.config is not None
        assert "output_dir" in video_processor.config
    
    def test_setup_default_config(self):
        """اختبار إعداد التكوين الافتراضي"""
        processor = VideoProcessor()
        
        assert processor.config["output_dir"] == "output"
        assert processor.config["save_annotated_video"] is True
        assert processor.config["frame_skip"] == 1
        assert "models" in processor.config
    
    def test_initialize_models(self, video_processor):
        """اختبار تهيئة النماذج"""
        # هذا الاختبار قد يفشل إذا لم تكن النماذج متوفرة
        try:
            success = video_processor.initialize_models()
            if success:
                assert video_processor.detector is not None
                assert video_processor.tracker is not None
                assert video_processor.pose_estimator is not None
                assert video_processor.event_classifier is not None
        except Exception as e:
            pytest.skip(f"تخطي الاختبار بسبب عدم توفر النماذج: {e}")
    
    def test_process_frame(self, video_processor):
        """اختبار معالجة إطار واحد"""
        # إنشاء إطار تجريبي
        frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        try:
            # محاولة تهيئة النماذج
            if not video_processor.initialize_models():
                pytest.skip("تخطي الاختبار بسبب عدم توفر النماذج")
            
            # معالجة الإطار
            result_frame = video_processor.process_frame(frame, 0, 0.0)
            
            assert result_frame is not None
            assert result_frame.shape == frame.shape
            
        except Exception as e:
            pytest.skip(f"تخطي الاختبار بسبب خطأ في النماذج: {e}")
    
    def test_get_ball_position(self, video_processor):
        """اختبار الحصول على موقع الكرة"""
        from backend.ai_models.base_model import BoundingBox
        
        # إنشاء كشوفات تجريبية
        detections = [
            BoundingBox(100, 100, 150, 150, 0.9, 0, "player"),
            BoundingBox(200, 200, 220, 220, 0.8, 1, "ball"),
            BoundingBox(300, 300, 350, 350, 0.7, 0, "player")
        ]
        
        ball_pos = video_processor._get_ball_position(detections)
        
        assert ball_pos is not None
        assert ball_pos == (210.0, 210.0)  # مركز الكرة
    
    def test_get_ball_position_no_ball(self, video_processor):
        """اختبار عدم وجود كرة"""
        from backend.ai_models.base_model import BoundingBox
        
        detections = [
            BoundingBox(100, 100, 150, 150, 0.9, 0, "player"),
            BoundingBox(300, 300, 350, 350, 0.7, 0, "player")
        ]
        
        ball_pos = video_processor._get_ball_position(detections)
        assert ball_pos is None
    
    def test_store_frame_data(self, video_processor):
        """اختبار حفظ بيانات الإطار"""
        from backend.ai_models.base_model import BoundingBox, DetectionResult, TrackingResult
        
        # إنشاء بيانات تجريبية
        detection_result = DetectionResult(
            frame_id=0,
            timestamp=0.0,
            bounding_boxes=[BoundingBox(100, 100, 150, 150, 0.9, 0, "player")],
            frame_shape=(480, 640, 3)
        )
        
        tracking_result = TrackingResult(
            frame_id=0,
            timestamp=0.0,
            tracks={1: BoundingBox(100, 100, 150, 150, 0.9, 0, "player")},
            frame_shape=(480, 640, 3)
        )
        
        video_processor._store_frame_data(
            0, 0.0, detection_result, tracking_result, [], []
        )
        
        assert len(video_processor.frame_data) == 1
        assert video_processor.frame_data[0]["frame_id"] == 0
    
    def test_calculate_statistics(self, video_processor):
        """اختبار حساب الإحصائيات"""
        # إضافة بيانات تجريبية
        video_processor.frame_data = [
            {
                "frame_id": 0,
                "timestamp": 0.0,
                "detections": [{"class_name": "player"}, {"class_name": "ball"}],
                "tracks": {"1": {"class_name": "player"}},
                "poses": [],
                "events": [{"event_type": "pass"}]
            },
            {
                "frame_id": 1,
                "timestamp": 0.033,
                "detections": [{"class_name": "player"}, {"class_name": "player"}],
                "tracks": {"1": {"class_name": "player"}, "2": {"class_name": "player"}},
                "poses": [],
                "events": []
            }
        ]
        
        stats = video_processor._calculate_statistics()
        
        assert stats["total_detections"] == 4
        assert stats["total_tracks"] == 3
        assert stats["total_events"] == 1
        assert stats["event_counts"]["pass"] == 1
        assert stats["average_players_per_frame"] == 1.5


class TestYOLODetector:
    """اختبارات كاشف YOLO"""
    
    @pytest.fixture
    def detector(self):
        """إنشاء كاشف YOLO للاختبار"""
        config = {
            "confidence_threshold": 0.5,
            "iou_threshold": 0.45
        }
        return YOLODetector(config=config)
    
    def test_detector_initialization(self, detector):
        """اختبار تهيئة الكاشف"""
        assert detector is not None
        assert detector.config["confidence_threshold"] == 0.5
        assert detector.config["iou_threshold"] == 0.45
    
    def test_validate_frame(self, detector):
        """اختبار التحقق من صحة الإطار"""
        # إطار صحيح
        valid_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        assert detector.validate_frame(valid_frame) is True
        
        # إطار فارغ
        assert detector.validate_frame(None) is False
        assert detector.validate_frame(np.array([])) is False
        
        # إطار بأبعاد خاطئة
        invalid_frame = np.random.randint(0, 255, (480, 640), dtype=np.uint8)
        assert detector.validate_frame(invalid_frame) is False
    
    def test_preprocess_frame(self, detector):
        """اختبار المعالجة المسبقة للإطار"""
        frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        processed = detector.preprocess_frame(frame)
        
        assert processed is not None
        assert processed.shape == frame.shape
    
    def test_classify_detection(self, detector):
        """اختبار تصنيف الكشف"""
        frame_shape = (480, 640, 3)
        
        # لاعب في المنتصف
        box = np.array([300, 200, 350, 300])
        class_name = detector._classify_detection(0, box, frame_shape)
        assert class_name in ["player", "goalkeeper", "referee"]
        
        # كرة
        class_name = detector._classify_detection(1, box, frame_shape)
        assert class_name == "ball"
    
    def test_filter_detections(self, detector):
        """اختبار تصفية الكشوفات"""
        from backend.ai_models.base_model import BoundingBox
        
        detections = [
            BoundingBox(100, 100, 150, 150, 0.9, 0, "player"),  # مساحة صحيحة
            BoundingBox(200, 200, 205, 205, 0.8, 1, "ball"),    # مساحة صغيرة جداً
            BoundingBox(0, 0, 500, 400, 0.7, 0, "player")       # مساحة كبيرة جداً
        ]
        
        filtered = detector.filter_detections(detections, min_area=100.0, max_area=10000.0)
        
        assert len(filtered) == 1
        assert filtered[0].class_name == "player"


class TestDeepSORTTracker:
    """اختبارات متتبع DeepSORT"""
    
    @pytest.fixture
    def tracker(self):
        """إنشاء متتبع للاختبار"""
        config = {
            "max_age": 30,
            "min_hits": 3,
            "iou_threshold": 0.3
        }
        return DeepSORTTracker(config=config)
    
    def test_tracker_initialization(self, tracker):
        """اختبار تهيئة المتتبع"""
        assert tracker is not None
        assert tracker.config["max_age"] == 30
        assert tracker.config["min_hits"] == 3
        assert len(tracker.tracks) == 0
        assert tracker.next_id == 1
    
    def test_compute_iou(self, tracker):
        """اختبار حساب IoU"""
        from backend.ai_models.base_model import BoundingBox
        
        bbox1 = BoundingBox(100, 100, 200, 200, 0.9, 0, "player")
        bbox2 = BoundingBox(150, 150, 250, 250, 0.8, 0, "player")
        
        iou = tracker._compute_iou(bbox1, bbox2)
        
        assert 0 <= iou <= 1
        assert iou > 0  # يجب أن يكون هناك تداخل
    
    def test_compute_iou_no_overlap(self, tracker):
        """اختبار IoU بدون تداخل"""
        from backend.ai_models.base_model import BoundingBox
        
        bbox1 = BoundingBox(100, 100, 200, 200, 0.9, 0, "player")
        bbox2 = BoundingBox(300, 300, 400, 400, 0.8, 0, "player")
        
        iou = tracker._compute_iou(bbox1, bbox2)
        assert iou == 0.0
    
    def test_create_new_track(self, tracker):
        """اختبار إنشاء مسار جديد"""
        from backend.ai_models.base_model import BoundingBox
        
        bbox = BoundingBox(100, 100, 200, 200, 0.9, 0, "player")
        feature = np.random.rand(128)
        
        initial_track_count = len(tracker.tracks)
        tracker._create_new_track(bbox, feature)
        
        assert len(tracker.tracks) == initial_track_count + 1
        assert tracker.next_id == 2
        assert tracker.tracks[-1].track_id == 1


if __name__ == "__main__":
    pytest.main([__file__])
