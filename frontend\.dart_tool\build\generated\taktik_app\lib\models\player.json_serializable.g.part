// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Player _$PlayerFromJson(Map<String, dynamic> json) => Player(
      id: json['id'] as String,
      trackId: (json['track_id'] as num).toInt(),
      jerseyNumber: (json['jersey_number'] as num?)?.toInt(),
      name: json['name'] as String?,
      position: json['position'] as String?,
      totalDistance: (json['total_distance'] as num).toDouble(),
      maxSpeed: (json['max_speed'] as num).toDouble(),
      averageSpeed: (json['average_speed'] as num).toDouble(),
      sprintCount: (json['sprint_count'] as num).toInt(),
      ballTouches: (json['ball_touches'] as num).toInt(),
      passesAttempted: (json['passes_attempted'] as num).toInt(),
      passesCompleted: (json['passes_completed'] as num).toInt(),
      passAccuracy: (json['pass_accuracy'] as num).toDouble(),
      shots: (json['shots'] as num).toInt(),
      shotsOnTarget: (json['shots_on_target'] as num).toInt(),
      goals: (json['goals'] as num).toInt(),
      assists: (json['assists'] as num).toInt(),
      tackles: (json['tackles'] as num).toInt(),
      interceptions: (json['interceptions'] as num).toInt(),
      clearances: (json['clearances'] as num).toInt(),
      blocks: (json['blocks'] as num).toInt(),
      dribbles: (json['dribbles'] as num).toInt(),
      crosses: (json['crosses'] as num).toInt(),
      headers: (json['headers'] as num).toInt(),
      foulsCommitted: (json['fouls_committed'] as num).toInt(),
      foulsReceived: (json['fouls_received'] as num).toInt(),
      yellowCards: (json['yellow_cards'] as num).toInt(),
      redCards: (json['red_cards'] as num).toInt(),
      technicalRating: (json['technical_rating'] as num).toDouble(),
      physicalRating: (json['physical_rating'] as num).toDouble(),
      tacticalRating: (json['tactical_rating'] as num).toDouble(),
      mentalRating: (json['mental_rating'] as num).toDouble(),
      overallRating: (json['overall_rating'] as num).toDouble(),
    );

Map<String, dynamic> _$PlayerToJson(Player instance) => <String, dynamic>{
      'id': instance.id,
      'track_id': instance.trackId,
      'jersey_number': instance.jerseyNumber,
      'name': instance.name,
      'position': instance.position,
      'total_distance': instance.totalDistance,
      'max_speed': instance.maxSpeed,
      'average_speed': instance.averageSpeed,
      'sprint_count': instance.sprintCount,
      'ball_touches': instance.ballTouches,
      'passes_attempted': instance.passesAttempted,
      'passes_completed': instance.passesCompleted,
      'pass_accuracy': instance.passAccuracy,
      'shots': instance.shots,
      'shots_on_target': instance.shotsOnTarget,
      'goals': instance.goals,
      'assists': instance.assists,
      'tackles': instance.tackles,
      'interceptions': instance.interceptions,
      'clearances': instance.clearances,
      'blocks': instance.blocks,
      'dribbles': instance.dribbles,
      'crosses': instance.crosses,
      'headers': instance.headers,
      'fouls_committed': instance.foulsCommitted,
      'fouls_received': instance.foulsReceived,
      'yellow_cards': instance.yellowCards,
      'red_cards': instance.redCards,
      'technical_rating': instance.technicalRating,
      'physical_rating': instance.physicalRating,
      'tactical_rating': instance.tacticalRating,
      'mental_rating': instance.mentalRating,
      'overall_rating': instance.overallRating,
    };
