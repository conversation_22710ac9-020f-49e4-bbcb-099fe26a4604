# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Project specific
uploads/
output/
logs/
models/*.pt
models/*.pth
models/*.onnx
data/videos/
data/datasets/
static/uploads/
*.db
*.sqlite
*.sqlite3

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Large files
*.mp4
*.avi
*.mov
*.mkv
*.wmv
*.flv
*.webm
*.m4v

# Model files
*.h5
*.hdf5
*.pkl
*.pickle

# Configuration files with secrets
config/secrets.yml
config/production.yml
.env.production
.env.local

# Backup files
*.bak
*.backup

# Compiled translations
*.mo

# Documentation builds
docs/build/
docs/_build/

# Coverage reports
htmlcov/
.coverage
coverage.xml

# Profiling
*.prof

# Memory dumps
*.dump
*.dmp

# Core dumps
core.*

# Jupyter
.jupyter/
*.ipynb

# PyCharm
.idea/

# VS Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
.vim/

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Node.js (if using frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Flutter (if using Flutter frontend)
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
build/
flutter_*.png
linked_*.ds
unlinked.ds
unlinked_spec.ds

# Generated files
*.generated.dart
*.freezed.dart
*.g.dart
*.mocks.dart

# Local database
taktik.db
taktik.db-journal

# Prometheus data
prometheus_data/

# Grafana data
grafana_data/

# Redis data
redis_data/

# PostgreSQL data
postgres_data/
