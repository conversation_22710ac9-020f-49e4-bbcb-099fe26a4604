"""
نموذج تصنيف أحداث المباراة
"""

import numpy as np
import cv2
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
from collections import deque
import torch
import torch.nn as nn
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

from ..base_model import BaseModel, ModelType, BoundingBox, EventResult


class EventType(Enum):
    """أنواع الأحداث في المباراة"""
    PASS = "pass"
    SHOT = "shot"
    GOAL = "goal"
    SAVE = "save"
    TACKLE = "tackle"
    FOUL = "foul"
    CORNER = "corner"
    THROW_IN = "throw_in"
    FREE_KICK = "free_kick"
    PENALTY = "penalty"
    OFFSIDE = "offside"
    YELLOW_CARD = "yellow_card"
    RED_CARD = "red_card"
    SUBSTITUTION = "substitution"
    DRIBBLE = "dribble"
    CROSS = "cross"
    HEADER = "header"
    CLEARANCE = "clearance"
    INTERCEPTION = "interception"
    UNKNOWN = "unknown"


class TemporalFeatureExtractor:
    """مستخرج الميزات الزمنية للأحداث"""
    
    def __init__(self, window_size: int = 30):
        self.window_size = window_size
        self.ball_positions = deque(maxlen=window_size)
        self.player_positions = deque(maxlen=window_size)
        self.player_actions = deque(maxlen=window_size)
        
    def update(self, ball_pos: Optional[Tuple[float, float]], 
               player_positions: Dict[int, Tuple[float, float]],
               player_actions: Dict[int, str]):
        """تحديث البيانات الزمنية"""
        self.ball_positions.append(ball_pos)
        self.player_positions.append(player_positions.copy())
        self.player_actions.append(player_actions.copy())
    
    def extract_features(self) -> np.ndarray:
        """استخراج الميزات من النافذة الزمنية"""
        features = []
        
        # ميزات الكرة
        ball_features = self._extract_ball_features()
        features.extend(ball_features)
        
        # ميزات اللاعبين
        player_features = self._extract_player_features()
        features.extend(player_features)
        
        # ميزات التفاعل
        interaction_features = self._extract_interaction_features()
        features.extend(interaction_features)
        
        return np.array(features)
    
    def _extract_ball_features(self) -> List[float]:
        """استخراج ميزات الكرة"""
        features = []
        
        # سرعة الكرة
        ball_speeds = []
        for i in range(1, len(self.ball_positions)):
            if self.ball_positions[i] and self.ball_positions[i-1]:
                speed = np.sqrt(
                    (self.ball_positions[i][0] - self.ball_positions[i-1][0])**2 +
                    (self.ball_positions[i][1] - self.ball_positions[i-1][1])**2
                )
                ball_speeds.append(speed)
        
        if ball_speeds:
            features.extend([
                np.mean(ball_speeds),      # متوسط السرعة
                np.max(ball_speeds),       # أقصى سرعة
                np.std(ball_speeds),       # انحراف السرعة
                len(ball_speeds)           # عدد الحركات
            ])
        else:
            features.extend([0, 0, 0, 0])
        
        # موقع الكرة الحالي
        if self.ball_positions and self.ball_positions[-1]:
            features.extend(list(self.ball_positions[-1]))
        else:
            features.extend([0, 0])
        
        # اتجاه حركة الكرة
        if len(self.ball_positions) >= 2 and self.ball_positions[-1] and self.ball_positions[-2]:
            direction = (
                self.ball_positions[-1][0] - self.ball_positions[-2][0],
                self.ball_positions[-1][1] - self.ball_positions[-2][1]
            )
            features.extend(direction)
        else:
            features.extend([0, 0])
        
        return features
    
    def _extract_player_features(self) -> List[float]:
        """استخراج ميزات اللاعبين"""
        features = []
        
        if not self.player_positions:
            return [0] * 20  # ميزات افتراضية
        
        current_positions = self.player_positions[-1]
        
        # عدد اللاعبين
        features.append(len(current_positions))
        
        # متوسط المسافات بين اللاعبين
        if len(current_positions) > 1:
            distances = []
            positions = list(current_positions.values())
            for i in range(len(positions)):
                for j in range(i+1, len(positions)):
                    dist = np.sqrt(
                        (positions[i][0] - positions[j][0])**2 +
                        (positions[i][1] - positions[j][1])**2
                    )
                    distances.append(dist)
            
            features.extend([
                np.mean(distances),
                np.min(distances),
                np.max(distances),
                np.std(distances)
            ])
        else:
            features.extend([0, 0, 0, 0])
        
        # سرعات اللاعبين
        player_speeds = []
        if len(self.player_positions) >= 2:
            prev_positions = self.player_positions[-2]
            for player_id in current_positions:
                if player_id in prev_positions:
                    speed = np.sqrt(
                        (current_positions[player_id][0] - prev_positions[player_id][0])**2 +
                        (current_positions[player_id][1] - prev_positions[player_id][1])**2
                    )
                    player_speeds.append(speed)
        
        if player_speeds:
            features.extend([
                np.mean(player_speeds),
                np.max(player_speeds),
                np.std(player_speeds)
            ])
        else:
            features.extend([0, 0, 0])
        
        # توزيع اللاعبين في الملعب
        if current_positions:
            x_positions = [pos[0] for pos in current_positions.values()]
            y_positions = [pos[1] for pos in current_positions.values()]
            
            features.extend([
                np.mean(x_positions),
                np.std(x_positions),
                np.mean(y_positions),
                np.std(y_positions),
                np.max(x_positions) - np.min(x_positions),  # انتشار أفقي
                np.max(y_positions) - np.min(y_positions)   # انتشار عمودي
            ])
        else:
            features.extend([0, 0, 0, 0, 0, 0])
        
        return features
    
    def _extract_interaction_features(self) -> List[float]:
        """استخراج ميزات التفاعل بين الكرة واللاعبين"""
        features = []
        
        if not self.ball_positions or not self.ball_positions[-1] or not self.player_positions:
            return [0] * 10
        
        ball_pos = self.ball_positions[-1]
        current_positions = self.player_positions[-1]
        
        # المسافات من الكرة إلى اللاعبين
        distances_to_ball = []
        for pos in current_positions.values():
            dist = np.sqrt((pos[0] - ball_pos[0])**2 + (pos[1] - ball_pos[1])**2)
            distances_to_ball.append(dist)
        
        if distances_to_ball:
            features.extend([
                np.min(distances_to_ball),    # أقرب لاعب للكرة
                np.mean(distances_to_ball),   # متوسط المسافة
                np.std(distances_to_ball)     # انحراف المسافة
            ])
            
            # عدد اللاعبين القريبين من الكرة
            close_players = sum(1 for d in distances_to_ball if d < 50)
            features.append(close_players)
        else:
            features.extend([0, 0, 0, 0])
        
        # تغيير ملكية الكرة
        ball_possession_changes = 0
        if len(self.player_positions) >= 2:
            # تحديد اللاعب الأقرب للكرة في الإطارين الأخيرين
            prev_closest = self._get_closest_player_to_ball(-2)
            curr_closest = self._get_closest_player_to_ball(-1)
            
            if prev_closest != curr_closest and prev_closest is not None and curr_closest is not None:
                ball_possession_changes = 1
        
        features.append(ball_possession_changes)
        
        # كثافة اللاعبين حول الكرة
        players_in_radius = sum(1 for d in distances_to_ball if d < 100)
        features.append(players_in_radius)
        
        # اتجاه حركة اللاعبين نحو الكرة
        if len(self.player_positions) >= 2:
            prev_positions = self.player_positions[-2]
            movements_toward_ball = 0
            
            for player_id in current_positions:
                if player_id in prev_positions:
                    prev_dist = np.sqrt(
                        (prev_positions[player_id][0] - ball_pos[0])**2 +
                        (prev_positions[player_id][1] - ball_pos[1])**2
                    )
                    curr_dist = np.sqrt(
                        (current_positions[player_id][0] - ball_pos[0])**2 +
                        (current_positions[player_id][1] - ball_pos[1])**2
                    )
                    
                    if curr_dist < prev_dist:  # يتحرك نحو الكرة
                        movements_toward_ball += 1
            
            features.append(movements_toward_ball)
        else:
            features.append(0)
        
        # ميزات إضافية
        features.extend([0, 0, 0])  # مساحة للميزات المستقبلية
        
        return features
    
    def _get_closest_player_to_ball(self, frame_offset: int) -> Optional[int]:
        """الحصول على اللاعب الأقرب للكرة في إطار معين"""
        if abs(frame_offset) >= len(self.ball_positions) or abs(frame_offset) >= len(self.player_positions):
            return None
        
        ball_pos = self.ball_positions[frame_offset]
        player_positions = self.player_positions[frame_offset]
        
        if not ball_pos or not player_positions:
            return None
        
        min_distance = float('inf')
        closest_player = None
        
        for player_id, pos in player_positions.items():
            distance = np.sqrt((pos[0] - ball_pos[0])**2 + (pos[1] - ball_pos[1])**2)
            if distance < min_distance:
                min_distance = distance
                closest_player = player_id
        
        return closest_player


class EventClassifier(BaseModel):
    """مصنف أحداث المباراة"""
    
    def __init__(self, model_path: Optional[str] = None, config: Optional[Dict] = None):
        default_config = {
            "window_size": 30,
            "confidence_threshold": 0.6,
            "feature_scaler": True,
            "model_type": "random_forest"  # أو "neural_network"
        }
        
        if config:
            default_config.update(config)
            
        super().__init__(model_path, default_config)
        
        self.feature_extractor = TemporalFeatureExtractor(self.config["window_size"])
        self.classifier = None
        self.scaler = StandardScaler() if self.config["feature_scaler"] else None
        
    def _get_model_type(self) -> ModelType:
        return ModelType.EVENT_DETECTION
    
    def load_model(self) -> bool:
        """تحميل نموذج التصنيف"""
        try:
            if self.config["model_type"] == "random_forest":
                self.classifier = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=10,
                    random_state=42
                )
            elif self.config["model_type"] == "neural_network":
                self.classifier = self._create_neural_network()
            
            if self.model_path:
                # تحميل نموذج مدرب مسبقاً
                import joblib
                saved_model = joblib.load(self.model_path)
                self.classifier = saved_model["classifier"]
                if self.scaler:
                    self.scaler = saved_model["scaler"]
            
            self.is_loaded = True
            print(f"تم تحميل مصنف الأحداث ({self.config['model_type']}) بنجاح")
            return True
            
        except Exception as e:
            print(f"خطأ في تحميل مصنف الأحداث: {e}")
            self.is_loaded = False
            return False
    
    def _create_neural_network(self) -> nn.Module:
        """إنشاء شبكة عصبية للتصنيف"""
        class EventNN(nn.Module):
            def __init__(self, input_size: int, num_classes: int):
                super().__init__()
                self.network = nn.Sequential(
                    nn.Linear(input_size, 128),
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(128, 64),
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(64, 32),
                    nn.ReLU(),
                    nn.Linear(32, num_classes),
                    nn.Softmax(dim=1)
                )
            
            def forward(self, x):
                return self.network(x)
        
        return EventNN(input_size=50, num_classes=len(EventType))  # تقدير حجم الميزات
    
    def update_temporal_data(self, ball_pos: Optional[Tuple[float, float]], 
                           player_positions: Dict[int, Tuple[float, float]],
                           player_actions: Dict[int, str]):
        """تحديث البيانات الزمنية"""
        self.feature_extractor.update(ball_pos, player_positions, player_actions)
    
    def predict(self, frame: np.ndarray, **kwargs) -> List[EventResult]:
        """تصنيف الأحداث في الإطار الحالي"""
        if not self.is_loaded:
            raise RuntimeError("النموذج غير محمل")
        
        frame_id = kwargs.get("frame_id", 0)
        timestamp = kwargs.get("timestamp", 0.0)
        
        # استخراج الميزات
        features = self.feature_extractor.extract_features()
        
        if features.size == 0:
            return []
        
        # تطبيق التطبيع إذا لزم الأمر
        if self.scaler:
            features = self.scaler.transform(features.reshape(1, -1))
        else:
            features = features.reshape(1, -1)
        
        # التنبؤ
        if hasattr(self.classifier, 'predict_proba'):
            probabilities = self.classifier.predict_proba(features)[0]
            predictions = [(i, prob) for i, prob in enumerate(probabilities) 
                          if prob > self.config["confidence_threshold"]]
        else:
            prediction = self.classifier.predict(features)[0]
            predictions = [(prediction, 1.0)]
        
        # تحويل إلى نتائج
        results = []
        for class_idx, confidence in predictions:
            event_type = list(EventType)[class_idx].value
            
            # تحديد المشاركين والموقع (تقدير أولي)
            participants = self._estimate_participants()
            location = self._estimate_event_location()
            
            result = EventResult(
                frame_id=frame_id,
                timestamp=timestamp,
                event_type=event_type,
                confidence=float(confidence),
                participants=participants,
                location=location,
                metadata={"features": features.tolist()}
            )
            
            results.append(result)
        
        return results
    
    def _estimate_participants(self) -> List[int]:
        """تقدير اللاعبين المشاركين في الحدث"""
        # تنفيذ مبسط - يمكن تحسينه
        if self.feature_extractor.player_positions:
            current_positions = self.feature_extractor.player_positions[-1]
            ball_pos = self.feature_extractor.ball_positions[-1]
            
            if ball_pos:
                # اللاعبين القريبين من الكرة
                close_players = []
                for player_id, pos in current_positions.items():
                    distance = np.sqrt((pos[0] - ball_pos[0])**2 + (pos[1] - ball_pos[1])**2)
                    if distance < 100:  # عتبة المسافة
                        close_players.append(player_id)
                
                return close_players[:3]  # أقرب 3 لاعبين
        
        return []
    
    def _estimate_event_location(self) -> Tuple[float, float]:
        """تقدير موقع الحدث"""
        if self.feature_extractor.ball_positions and self.feature_extractor.ball_positions[-1]:
            return self.feature_extractor.ball_positions[-1]
        
        return (0.0, 0.0)
