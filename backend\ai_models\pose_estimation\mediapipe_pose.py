"""
نموذج تحليل الحركات والوضعيات باستخدام MediaPipe
"""

import cv2
import numpy as np
# import mediapipe as mp  # تعطيل مؤقت
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum

from ..base_model import BaseModel, ModelType, BoundingBox, PoseKeypoint, PoseResult


class PoseAction(Enum):
    """أنواع الحركات المختلفة"""
    STANDING = "standing"
    RUNNING = "running"
    WALKING = "walking"
    JUMPING = "jumping"
    KICKING = "kicking"
    HEADING = "heading"
    SLIDING = "sliding"
    GOALKEEPING = "goalkeeping"
    UNKNOWN = "unknown"


class MediaPipePose(BaseModel):
    """محلل الوضعيات باستخدام MediaPipe"""

    # نقاط المفاصل في MediaPipe
    POSE_LANDMARKS = {
        'nose': 0,
        'left_eye_inner': 1, 'left_eye': 2, 'left_eye_outer': 3,
        'right_eye_inner': 4, 'right_eye': 5, 'right_eye_outer': 6,
        'left_ear': 7, 'right_ear': 8,
        'mouth_left': 9, 'mouth_right': 10,
        'left_shoulder': 11, 'right_shoulder': 12,
        'left_elbow': 13, 'right_elbow': 14,
        'left_wrist': 15, 'right_wrist': 16,
        'left_pinky': 17, 'right_pinky': 18,
        'left_index': 19, 'right_index': 20,
        'left_thumb': 21, 'right_thumb': 22,
        'left_hip': 23, 'right_hip': 24,
        'left_knee': 25, 'right_knee': 26,
        'left_ankle': 27, 'right_ankle': 28,
        'left_heel': 29, 'right_heel': 30,
        'left_foot_index': 31, 'right_foot_index': 32
    }

    def __init__(self, model_path: Optional[str] = None, config: Optional[Dict] = None):
        default_config = {
            "min_detection_confidence": 0.5,
            "min_tracking_confidence": 0.5,
            "model_complexity": 1,  # 0, 1, or 2
            "smooth_landmarks": True,
            "enable_segmentation": False,
            "smooth_segmentation": True
        }

        if config:
            default_config.update(config)

        super().__init__(model_path, default_config)
        self.mp_pose = None
        self.pose = None
        self.mp_drawing = None

    def _get_model_type(self) -> ModelType:
        return ModelType.POSE_ESTIMATION

    def load_model(self) -> bool:
        """تحميل نموذج MediaPipe Pose"""
        try:
            # نسخة مبسطة بدون MediaPipe
            print("⚠️ MediaPipe غير متوفر - استخدام نسخة مبسطة")
            self.is_loaded = True
            return True

        except Exception as e:
            print(f"خطأ في تحميل نموذج MediaPipe Pose: {e}")
            self.is_loaded = False
            return False

    def predict(self, frame: np.ndarray, person_bbox: BoundingBox, **kwargs) -> Optional[PoseResult]:
        """تحليل وضعية شخص واحد"""
        if not self.is_loaded:
            raise RuntimeError("النموذج غير محمل")

        frame_id = kwargs.get("frame_id", 0)
        timestamp = kwargs.get("timestamp", 0.0)
        person_id = kwargs.get("person_id", 0)

        # نسخة مبسطة - إرجاع نتيجة وهمية
        keypoints = self._create_dummy_keypoints(person_bbox)

        return PoseResult(
            frame_id=frame_id,
            timestamp=timestamp,
            person_id=person_id,
            keypoints=keypoints,
            bbox=person_bbox
        )

    def _create_dummy_keypoints(self, bbox: BoundingBox) -> Dict[str, PoseKeypoint]:
        """إنشاء نقاط مفصلية وهمية للاختبار"""
        keypoints = {}
        center_x = (bbox.x1 + bbox.x2) / 2
        center_y = (bbox.y1 + bbox.y2) / 2

        # نقاط أساسية
        keypoints['nose'] = PoseKeypoint(center_x, bbox.y1 + 20, 0.8, True)
        keypoints['left_shoulder'] = PoseKeypoint(center_x - 30, center_y - 50, 0.8, True)
        keypoints['right_shoulder'] = PoseKeypoint(center_x + 30, center_y - 50, 0.8, True)
        keypoints['left_hip'] = PoseKeypoint(center_x - 20, center_y + 20, 0.8, True)
        keypoints['right_hip'] = PoseKeypoint(center_x + 20, center_y + 20, 0.8, True)
        keypoints['left_knee'] = PoseKeypoint(center_x - 25, center_y + 60, 0.8, True)
        keypoints['right_knee'] = PoseKeypoint(center_x + 25, center_y + 60, 0.8, True)
        keypoints['left_ankle'] = PoseKeypoint(center_x - 30, bbox.y2 - 10, 0.8, True)
        keypoints['right_ankle'] = PoseKeypoint(center_x + 30, bbox.y2 - 10, 0.8, True)

        return keypoints

    def predict_multiple(self, frame: np.ndarray, person_bboxes: List[BoundingBox], **kwargs) -> List[PoseResult]:
        """تحليل وضعيات متعددة"""
        results = []

        for i, bbox in enumerate(person_bboxes):
            kwargs_copy = kwargs.copy()
            kwargs_copy["person_id"] = kwargs.get("person_ids", [i])[i] if "person_ids" in kwargs else i

            pose_result = self.predict(frame, bbox, **kwargs_copy)
            if pose_result:
                results.append(pose_result)

        return results

    def _extract_keypoints(self, landmarks, crop_shape: Tuple[int, int, int],
                          offset: Tuple[int, int]) -> Dict[str, PoseKeypoint]:
        """استخراج النقاط المفصلية"""
        keypoints = {}
        h, w = crop_shape[:2]
        offset_x, offset_y = offset

        for name, idx in self.POSE_LANDMARKS.items():
            if idx < len(landmarks.landmark):
                landmark = landmarks.landmark[idx]

                # تحويل الإحداثيات النسبية إلى مطلقة
                x = landmark.x * w + offset_x
                y = landmark.y * h + offset_y

                keypoint = PoseKeypoint(
                    x=float(x),
                    y=float(y),
                    confidence=float(landmark.visibility),
                    visible=landmark.visibility > 0.5
                )

                keypoints[name] = keypoint

        return keypoints

    def analyze_action(self, pose_result: PoseResult) -> PoseAction:
        """تحليل نوع الحركة من الوضعية"""
        keypoints = pose_result.keypoints

        # التحقق من وجود النقاط المطلوبة
        required_points = ['left_hip', 'right_hip', 'left_knee', 'right_knee',
                          'left_ankle', 'right_ankle', 'left_shoulder', 'right_shoulder']

        if not all(point in keypoints and keypoints[point].visible for point in required_points):
            return PoseAction.UNKNOWN

        # حساب الزوايا والمسافات
        hip_center = self._get_midpoint(keypoints['left_hip'], keypoints['right_hip'])
        shoulder_center = self._get_midpoint(keypoints['left_shoulder'], keypoints['right_shoulder'])

        # زاوية الجذع
        torso_angle = self._calculate_angle(hip_center, shoulder_center, (shoulder_center[0], 0))

        # زوايا الركبتين
        left_knee_angle = self._calculate_knee_angle(
            keypoints['left_hip'], keypoints['left_knee'], keypoints['left_ankle']
        )
        right_knee_angle = self._calculate_knee_angle(
            keypoints['right_hip'], keypoints['right_knee'], keypoints['right_ankle']
        )

        # ارتفاع القدمين
        left_foot_height = keypoints['left_ankle'].y
        right_foot_height = keypoints['right_ankle'].y
        foot_height_diff = abs(left_foot_height - right_foot_height)

        # تصنيف الحركة
        if foot_height_diff > 50:  # إحدى القدمين مرفوعة
            if min(left_knee_angle, right_knee_angle) < 90:  # ركبة مثنية بشدة
                return PoseAction.KICKING
            else:
                return PoseAction.RUNNING

        elif abs(torso_angle) > 30:  # الجذع مائل
            return PoseAction.SLIDING

        elif max(left_knee_angle, right_knee_angle) < 120:  # الركبتان مثنيتان
            return PoseAction.JUMPING

        elif shoulder_center[1] < hip_center[1] - 20:  # الكتفان أعلى من الوركين بوضوح
            return PoseAction.STANDING

        else:
            return PoseAction.WALKING

    def _get_midpoint(self, point1: PoseKeypoint, point2: PoseKeypoint) -> Tuple[float, float]:
        """حساب النقطة الوسطى بين نقطتين"""
        return ((point1.x + point2.x) / 2, (point1.y + point2.y) / 2)

    def _calculate_angle(self, point1: Tuple[float, float],
                        point2: Tuple[float, float],
                        point3: Tuple[float, float]) -> float:
        """حساب الزاوية بين ثلاث نقاط"""
        # المتجهات
        v1 = (point1[0] - point2[0], point1[1] - point2[1])
        v2 = (point3[0] - point2[0], point3[1] - point2[1])

        # الضرب النقطي
        dot_product = v1[0] * v2[0] + v1[1] * v2[1]

        # المقادير
        magnitude1 = np.sqrt(v1[0]**2 + v1[1]**2)
        magnitude2 = np.sqrt(v2[0]**2 + v2[1]**2)

        if magnitude1 == 0 or magnitude2 == 0:
            return 0

        # الزاوية بالراديان ثم بالدرجات
        cos_angle = dot_product / (magnitude1 * magnitude2)
        cos_angle = np.clip(cos_angle, -1, 1)  # تجنب أخطاء الحوسبة
        angle = np.arccos(cos_angle)

        return np.degrees(angle)

    def _calculate_knee_angle(self, hip: PoseKeypoint, knee: PoseKeypoint, ankle: PoseKeypoint) -> float:
        """حساب زاوية الركبة"""
        return self._calculate_angle((hip.x, hip.y), (knee.x, knee.y), (ankle.x, ankle.y))

    def calculate_speed(self, pose_history: List[PoseResult], fps: float = 30.0) -> float:
        """حساب سرعة الحركة من تاريخ الوضعيات"""
        if len(pose_history) < 2:
            return 0.0

        distances = []
        for i in range(1, len(pose_history)):
            prev_pose = pose_history[i-1]
            curr_pose = pose_history[i]

            # حساب المسافة بين مراكز الوركين
            if 'left_hip' in prev_pose.keypoints and 'right_hip' in prev_pose.keypoints and \
               'left_hip' in curr_pose.keypoints and 'right_hip' in curr_pose.keypoints:

                prev_center = self._get_midpoint(prev_pose.keypoints['left_hip'], prev_pose.keypoints['right_hip'])
                curr_center = self._get_midpoint(curr_pose.keypoints['left_hip'], curr_pose.keypoints['right_hip'])

                distance = np.sqrt((curr_center[0] - prev_center[0])**2 + (curr_center[1] - prev_center[1])**2)
                distances.append(distance)

        if not distances:
            return 0.0

        # متوسط المسافة لكل إطار
        avg_distance_per_frame = np.mean(distances)

        # تحويل إلى سرعة (بكسل/ثانية)
        speed = avg_distance_per_frame * fps

        return speed

    def draw_pose(self, frame: np.ndarray, pose_result: PoseResult) -> np.ndarray:
        """رسم الوضعية على الإطار"""
        result_frame = frame.copy()

        # رسم النقاط المفصلية
        for name, keypoint in pose_result.keypoints.items():
            if keypoint.visible:
                cv2.circle(
                    result_frame,
                    (int(keypoint.x), int(keypoint.y)),
                    3,
                    (0, 255, 0),
                    -1
                )

        # رسم الخطوط بين المفاصل
        connections = [
            ('left_shoulder', 'right_shoulder'),
            ('left_shoulder', 'left_elbow'),
            ('left_elbow', 'left_wrist'),
            ('right_shoulder', 'right_elbow'),
            ('right_elbow', 'right_wrist'),
            ('left_shoulder', 'left_hip'),
            ('right_shoulder', 'right_hip'),
            ('left_hip', 'right_hip'),
            ('left_hip', 'left_knee'),
            ('left_knee', 'left_ankle'),
            ('right_hip', 'right_knee'),
            ('right_knee', 'right_ankle')
        ]

        for start_point, end_point in connections:
            if (start_point in pose_result.keypoints and end_point in pose_result.keypoints and
                pose_result.keypoints[start_point].visible and pose_result.keypoints[end_point].visible):

                start_pos = (int(pose_result.keypoints[start_point].x), int(pose_result.keypoints[start_point].y))
                end_pos = (int(pose_result.keypoints[end_point].x), int(pose_result.keypoints[end_point].y))

                cv2.line(result_frame, start_pos, end_pos, (255, 0, 0), 2)

        return result_frame
