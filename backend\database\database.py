"""
إدارة قاعدة البيانات
"""

import os
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from contextlib import contextmanager
from typing import Generator, Optional, Dict, Any, List
import logging

from .models import Base, Match, Team, Player, Event, FrameData, Formation, Analysis, UserSession, ProcessingJob

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, database_url: Optional[str] = None):
        """
        تهيئة مدير قاعدة البيانات
        
        Args:
            database_url: رابط قاعدة البيانات. إذا لم يتم تحديده، سيتم استخدام SQLite
        """
        if database_url is None:
            # استخدام SQLite كقاعدة بيانات افتراضية
            database_url = "sqlite:///taktik.db"
        
        self.database_url = database_url
        self.engine = None
        self.SessionLocal = None
        
        self._setup_database()
    
    def _setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            # إنشاء محرك قاعدة البيانات
            if self.database_url.startswith("sqlite"):
                self.engine = create_engine(
                    self.database_url,
                    poolclass=StaticPool,
                    connect_args={"check_same_thread": False},
                    echo=False  # تعيين True لرؤية استعلامات SQL
                )
            else:
                self.engine = create_engine(
                    self.database_url,
                    pool_pre_ping=True,
                    echo=False
                )
            
            # إنشاء جلسة قاعدة البيانات
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # إنشاء الجداول
            self.create_tables()
            
            logger.info(f"تم إعداد قاعدة البيانات بنجاح: {self.database_url}")
            
        except Exception as e:
            logger.error(f"خطأ في إعداد قاعدة البيانات: {e}")
            raise
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("تم إنشاء جداول قاعدة البيانات")
        except Exception as e:
            logger.error(f"خطأ في إنشاء الجداول: {e}")
            raise
    
    def drop_tables(self):
        """حذف جميع الجداول"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.info("تم حذف جميع الجداول")
        except Exception as e:
            logger.error(f"خطأ في حذف الجداول: {e}")
            raise
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """الحصول على جلسة قاعدة البيانات"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"خطأ في جلسة قاعدة البيانات: {e}")
            raise
        finally:
            session.close()
    
    def get_session_sync(self) -> Session:
        """الحصول على جلسة متزامنة"""
        return self.SessionLocal()
    
    # ==================== عمليات المباريات ====================
    
    def create_match(self, match_data: Dict[str, Any]) -> str:
        """إنشاء مباراة جديدة"""
        with self.get_session() as session:
            match = Match(**match_data)
            session.add(match)
            session.flush()
            return match.id
    
    def get_match(self, match_id: str) -> Optional[Match]:
        """الحصول على مباراة بالمعرف"""
        with self.get_session() as session:
            return session.query(Match).filter(Match.id == match_id).first()
    
    def get_all_matches(self, limit: int = 100, offset: int = 0) -> List[Match]:
        """الحصول على جميع المباريات"""
        with self.get_session() as session:
            return session.query(Match).offset(offset).limit(limit).all()
    
    def update_match(self, match_id: str, update_data: Dict[str, Any]) -> bool:
        """تحديث بيانات المباراة"""
        with self.get_session() as session:
            result = session.query(Match).filter(Match.id == match_id).update(update_data)
            return result > 0
    
    def delete_match(self, match_id: str) -> bool:
        """حذف مباراة"""
        with self.get_session() as session:
            result = session.query(Match).filter(Match.id == match_id).delete()
            return result > 0
    
    # ==================== عمليات الفرق ====================
    
    def create_team(self, team_data: Dict[str, Any]) -> str:
        """إنشاء فريق جديد"""
        with self.get_session() as session:
            team = Team(**team_data)
            session.add(team)
            session.flush()
            return team.id
    
    def get_teams_by_match(self, match_id: str) -> List[Team]:
        """الحصول على فرق المباراة"""
        with self.get_session() as session:
            return session.query(Team).filter(Team.match_id == match_id).all()
    
    def update_team_stats(self, team_id: str, stats_data: Dict[str, Any]) -> bool:
        """تحديث إحصائيات الفريق"""
        with self.get_session() as session:
            result = session.query(Team).filter(Team.id == team_id).update(stats_data)
            return result > 0
    
    # ==================== عمليات اللاعبين ====================
    
    def create_player(self, player_data: Dict[str, Any]) -> str:
        """إنشاء لاعب جديد"""
        with self.get_session() as session:
            player = Player(**player_data)
            session.add(player)
            session.flush()
            return player.id
    
    def get_players_by_match(self, match_id: str) -> List[Player]:
        """الحصول على لاعبي المباراة"""
        with self.get_session() as session:
            return session.query(Player).filter(Player.match_id == match_id).all()
    
    def get_players_by_team(self, team_id: str) -> List[Player]:
        """الحصول على لاعبي الفريق"""
        with self.get_session() as session:
            return session.query(Player).filter(Player.team_id == team_id).all()
    
    def update_player_stats(self, player_id: str, stats_data: Dict[str, Any]) -> bool:
        """تحديث إحصائيات اللاعب"""
        with self.get_session() as session:
            result = session.query(Player).filter(Player.id == player_id).update(stats_data)
            return result > 0
    
    def get_player_by_track_id(self, match_id: str, track_id: int) -> Optional[Player]:
        """الحصول على لاعب بمعرف التتبع"""
        with self.get_session() as session:
            return session.query(Player).filter(
                Player.match_id == match_id,
                Player.track_id == track_id
            ).first()
    
    # ==================== عمليات الأحداث ====================
    
    def create_event(self, event_data: Dict[str, Any]) -> str:
        """إنشاء حدث جديد"""
        with self.get_session() as session:
            event = Event(**event_data)
            session.add(event)
            session.flush()
            return event.id
    
    def create_events_batch(self, events_data: List[Dict[str, Any]]) -> List[str]:
        """إنشاء مجموعة أحداث"""
        with self.get_session() as session:
            events = [Event(**event_data) for event_data in events_data]
            session.add_all(events)
            session.flush()
            return [event.id for event in events]
    
    def get_events_by_match(self, match_id: str, event_type: Optional[str] = None) -> List[Event]:
        """الحصول على أحداث المباراة"""
        with self.get_session() as session:
            query = session.query(Event).filter(Event.match_id == match_id)
            if event_type:
                query = query.filter(Event.event_type == event_type)
            return query.order_by(Event.timestamp).all()
    
    def get_events_by_player(self, player_id: str) -> List[Event]:
        """الحصول على أحداث اللاعب"""
        with self.get_session() as session:
            return session.query(Event).filter(Event.player_id == player_id).order_by(Event.timestamp).all()
    
    # ==================== عمليات بيانات الإطارات ====================
    
    def create_frame_data(self, frame_data: Dict[str, Any]) -> str:
        """إنشاء بيانات إطار"""
        with self.get_session() as session:
            frame = FrameData(**frame_data)
            session.add(frame)
            session.flush()
            return frame.id
    
    def create_frame_data_batch(self, frames_data: List[Dict[str, Any]]) -> List[str]:
        """إنشاء مجموعة بيانات إطارات"""
        with self.get_session() as session:
            frames = [FrameData(**frame_data) for frame_data in frames_data]
            session.add_all(frames)
            session.flush()
            return [frame.id for frame in frames]
    
    def get_frame_data_by_match(self, match_id: str, start_frame: int = 0, end_frame: Optional[int] = None) -> List[FrameData]:
        """الحصول على بيانات إطارات المباراة"""
        with self.get_session() as session:
            query = session.query(FrameData).filter(
                FrameData.match_id == match_id,
                FrameData.frame_id >= start_frame
            )
            if end_frame is not None:
                query = query.filter(FrameData.frame_id <= end_frame)
            return query.order_by(FrameData.frame_id).all()
    
    # ==================== عمليات التشكيلات ====================
    
    def create_formation(self, formation_data: Dict[str, Any]) -> str:
        """إنشاء تشكيل تكتيكي"""
        with self.get_session() as session:
            formation = Formation(**formation_data)
            session.add(formation)
            session.flush()
            return formation.id
    
    def get_formations_by_match(self, match_id: str) -> List[Formation]:
        """الحصول على تشكيلات المباراة"""
        with self.get_session() as session:
            return session.query(Formation).filter(Formation.match_id == match_id).order_by(Formation.start_time).all()
    
    # ==================== عمليات التحليلات ====================
    
    def create_analysis(self, analysis_data: Dict[str, Any]) -> str:
        """إنشاء تحليل"""
        with self.get_session() as session:
            analysis = Analysis(**analysis_data)
            session.add(analysis)
            session.flush()
            return analysis.id
    
    def get_analyses_by_match(self, match_id: str) -> List[Analysis]:
        """الحصول على تحليلات المباراة"""
        with self.get_session() as session:
            return session.query(Analysis).filter(Analysis.match_id == match_id).all()
    
    # ==================== عمليات مهام المعالجة ====================
    
    def create_processing_job(self, job_data: Dict[str, Any]) -> str:
        """إنشاء مهمة معالجة"""
        with self.get_session() as session:
            job = ProcessingJob(**job_data)
            session.add(job)
            session.flush()
            return job.id
    
    def update_job_progress(self, job_id: str, progress: float, status: Optional[str] = None) -> bool:
        """تحديث تقدم المهمة"""
        update_data = {"progress": progress}
        if status:
            update_data["status"] = status
        
        with self.get_session() as session:
            result = session.query(ProcessingJob).filter(ProcessingJob.id == job_id).update(update_data)
            return result > 0
    
    def get_pending_jobs(self) -> List[ProcessingJob]:
        """الحصول على المهام المعلقة"""
        with self.get_session() as session:
            return session.query(ProcessingJob).filter(ProcessingJob.status == "pending").all()
    
    # ==================== إحصائيات عامة ====================
    
    def get_database_stats(self) -> Dict[str, int]:
        """الحصول على إحصائيات قاعدة البيانات"""
        with self.get_session() as session:
            stats = {
                "matches": session.query(Match).count(),
                "teams": session.query(Team).count(),
                "players": session.query(Player).count(),
                "events": session.query(Event).count(),
                "frame_data": session.query(FrameData).count(),
                "formations": session.query(Formation).count(),
                "analyses": session.query(Analysis).count(),
                "processing_jobs": session.query(ProcessingJob).count()
            }
            return stats
    
    def cleanup_old_data(self, days: int = 30) -> Dict[str, int]:
        """تنظيف البيانات القديمة"""
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        deleted_counts = {}
        
        with self.get_session() as session:
            # حذف الجلسات المنتهية الصلاحية
            deleted_sessions = session.query(UserSession).filter(
                UserSession.expires_at < datetime.utcnow()
            ).delete()
            deleted_counts["expired_sessions"] = deleted_sessions
            
            # حذف المهام المكتملة القديمة
            deleted_jobs = session.query(ProcessingJob).filter(
                ProcessingJob.status == "completed",
                ProcessingJob.completed_at < cutoff_date
            ).delete()
            deleted_counts["old_jobs"] = deleted_jobs
        
        return deleted_counts


# مثيل عام لمدير قاعدة البيانات
db_manager = DatabaseManager()
