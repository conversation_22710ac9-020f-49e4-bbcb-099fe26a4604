events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # إعدادات التسجيل
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # إعدادات الأداء
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 500M;  # للسماح بتحميل ملفات فيديو كبيرة

    # ضغط الملفات
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # خادم التطبيق الرئيسي
    upstream taktik_backend {
        server taktik-app:8000;
    }

    # إعادة توجيه HTTP إلى HTTPS (اختياري)
    server {
        listen 80;
        server_name _;
        
        # السماح بالوصول المباشر للتطوير
        location / {
            proxy_pass http://taktik_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # إعدادات WebSocket (للتحديثات المباشرة)
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # مهلة زمنية طويلة لمعالجة الفيديو
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
        }

        # خدمة الملفات الثابتة
        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # خدمة ملفات المخرجات
        location /output/ {
            alias /var/www/output/;
            expires 1d;
            add_header Cache-Control "public";
            
            # حماية الملفات الحساسة
            location ~* \.(json|csv)$ {
                add_header Content-Disposition "attachment";
            }
        }

        # API التوثيق
        location /docs {
            proxy_pass http://taktik_backend/docs;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /redoc {
            proxy_pass http://taktik_backend/redoc;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # فحص الصحة
        location /health {
            proxy_pass http://taktik_backend/health;
            access_log off;
        }

        # حماية من الملفات الحساسة
        location ~ /\. {
            deny all;
        }

        location ~ \.(env|ini|conf|log)$ {
            deny all;
        }
    }

    # خادم HTTPS (للإنتاج)
    # server {
    #     listen 443 ssl http2;
    #     server_name your-domain.com;
    #     
    #     ssl_certificate /path/to/certificate.crt;
    #     ssl_certificate_key /path/to/private.key;
    #     
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     
    #     location / {
    #         proxy_pass http://taktik_backend;
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto $scheme;
    #         
    #         proxy_http_version 1.1;
    #         proxy_set_header Upgrade $http_upgrade;
    #         proxy_set_header Connection "upgrade";
    #         
    #         proxy_read_timeout 300s;
    #         proxy_connect_timeout 75s;
    #     }
    #     
    #     location /static/ {
    #         alias /var/www/static/;
    #         expires 1y;
    #         add_header Cache-Control "public, immutable";
    #     }
    #     
    #     location /output/ {
    #         alias /var/www/output/;
    #         expires 1d;
    #         add_header Cache-Control "public";
    #     }
    # }
}
