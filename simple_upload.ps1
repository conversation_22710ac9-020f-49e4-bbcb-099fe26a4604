# Simple video upload script for Taktik

$MatchId = "f94b3a1c-e800-429c-a38e-825ce88bfd88"
$VideoPath = "small_test.mp4"
$uri = "http://localhost:8000/matches/$MatchId/upload-video"

Write-Host "Uploading video..."
Write-Host "File: $VideoPath"
Write-Host "Match: $MatchId"

if (Test-Path $VideoPath) {
    try {
        $fileBytes = [System.IO.File]::ReadAllBytes($VideoPath)
        $fileName = [System.IO.Path]::GetFileName($VideoPath)
        
        $boundary = [System.Guid]::NewGuid().ToString()
        $LF = "`r`n"
        
        $bodyLines = @(
            "--$boundary",
            "Content-Disposition: form-data; name=`"file`"; filename=`"$fileName`"",
            "Content-Type: video/mp4$LF",
            [System.Text.Encoding]::GetEncoding("iso-8859-1").GetString($fileBytes),
            "--$boundary--$LF"
        ) -join $LF
        
        $response = Invoke-RestMethod -Uri $uri -Method Post -ContentType "multipart/form-data; boundary=$boundary" -Body $bodyLines
        
        Write-Host "SUCCESS! Video uploaded successfully!"
        $response | ConvertTo-Json
        
    } catch {
        Write-Host "ERROR: $($_.Exception.Message)"
    }
} else {
    Write-Host "ERROR: File not found: $VideoPath"
}
