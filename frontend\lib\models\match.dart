import 'package:json_annotation/json_annotation.dart';

part 'match.g.dart';

@JsonSerializable()
class Match {
  final String id;
  final String name;
  final DateTime? date;
  final double? duration;
  @Json<PERSON><PERSON>(name: 'video_path')
  final String? videoPath;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'video_fps')
  final double? videoFps;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'video_width')
  final int? videoWidth;
  @<PERSON>son<PERSON>ey(name: 'video_height')
  final int? videoHeight;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'processing_status')
  final String processingStatus;
  @<PERSON>son<PERSON><PERSON>(name: 'processing_progress')
  final double processingProgress;
  @<PERSON>sonKey(name: 'total_frames')
  final int? totalFrames;
  @Json<PERSON>ey(name: 'processed_frames')
  final int? processedFrames;
  @Json<PERSON>ey(name: 'total_events')
  final int? totalEvents;
  @<PERSON>sonKey(name: 'ball_in_play_time')
  final double? ballInPlayTime;
  @<PERSON>son<PERSON>ey(name: 'created_at')
  final DateTime? createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime? updatedAt;

  Match({
    required this.id,
    required this.name,
    this.date,
    this.duration,
    this.videoPath,
    this.videoFps,
    this.videoWidth,
    this.videoHeight,
    required this.processingStatus,
    required this.processingProgress,
    this.totalFrames,
    this.processedFrames,
    this.totalEvents,
    this.ballInPlayTime,
    this.createdAt,
    this.updatedAt,
  });

  factory Match.fromJson(Map<String, dynamic> json) => _$MatchFromJson(json);
  Map<String, dynamic> toJson() => _$MatchToJson(this);

  // Helper methods
  bool get hasVideo => videoPath != null && videoPath!.isNotEmpty;
  bool get isProcessing => processingStatus == 'processing';
  bool get isCompleted => processingStatus == 'completed';
  bool get isFailed => processingStatus == 'failed';
  bool get isPending => processingStatus == 'pending';

  String get statusText {
    switch (processingStatus) {
      case 'pending':
        return 'في الانتظار';
      case 'processing':
        return 'جاري المعالجة';
      case 'completed':
        return 'مكتملة';
      case 'failed':
        return 'فشلت';
      default:
        return 'غير معروف';
    }
  }

  String get durationText {
    if (duration == null) return 'غير محدد';
    final minutes = (duration! / 60).round();
    return '$minutes دقيقة';
  }

  Match copyWith({
    String? id,
    String? name,
    DateTime? date,
    double? duration,
    String? videoPath,
    double? videoFps,
    int? videoWidth,
    int? videoHeight,
    String? processingStatus,
    double? processingProgress,
    int? totalFrames,
    int? processedFrames,
    int? totalEvents,
    double? ballInPlayTime,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Match(
      id: id ?? this.id,
      name: name ?? this.name,
      date: date ?? this.date,
      duration: duration ?? this.duration,
      videoPath: videoPath ?? this.videoPath,
      videoFps: videoFps ?? this.videoFps,
      videoWidth: videoWidth ?? this.videoWidth,
      videoHeight: videoHeight ?? this.videoHeight,
      processingStatus: processingStatus ?? this.processingStatus,
      processingProgress: processingProgress ?? this.processingProgress,
      totalFrames: totalFrames ?? this.totalFrames,
      processedFrames: processedFrames ?? this.processedFrames,
      totalEvents: totalEvents ?? this.totalEvents,
      ballInPlayTime: ballInPlayTime ?? this.ballInPlayTime,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
