["retrofit_generator on lib/services/api_service.dart", ["", "This builder requires Dart inputs without syntax errors.\nHowever, package:taktik_app/services/api_service.dart (or an existing part) contains the following errors.\napi_service.dart:44:31: Named parameters must be enclosed in curly braces ('{' and '}').\napi_service.dart:45:33: Named parameters must be enclosed in curly braces ('{' and '}').\n\nTry fixing the errors and re-running the build.\n", "#0      AnalyzerResolver.compilationUnitFor.<anonymous closure> (package:build_resolvers/src/resolver.dart:244:9)\n#1      Pool.withResource (package:pool/pool.dart:127:28)\n<asynchronous suspension>\n#2      _hasAnyTopLevelAnnotations (package:source_gen/src/builder.dart:363:18)\n<asynchronous suspension>\n#3      _Builder.build (package:source_gen/src/builder.dart:89:11)\n<asynchronous suspension>\n#4      runBuilder.buildForInput (package:build/src/generate/run_builder.dart:83:7)\n<asynchronous suspension>\n#5      Future.wait.<anonymous closure> (dart:async/future.dart:525:21)\n<asynchronous suspension>\n#6      scopeLogAsync.<anonymous closure> (package:build/src/builder/logging.dart:32:40)\n<asynchronous suspension>\n"]]