# ⚽ Taktik - نظام تحليل مباريات كرة القدم الذكي

نظام ذكاء اصطناعي متكامل لتحليل مباريات كرة القدم باستخدام الفيديوهات، وتحويلها إلى بيانات تفصيلية تشمل التكتيك، الإحصائيات، والتقييمات.

## 🎯 الميزات الرئيسية

- **كشف وتتبع اللاعبين**: استخدام YOLOv8 و DeepSORT
- **تحليل التكتيك**: اكتشاف التشكيلات والخطط التكتيكية
- **إحصائيات شاملة**: تمريرات، تسديدات، استحواذ، وأكثر
- **تقييم الأداء**: تحليل الأداء الفني والبدني والنفسي
- **خرائط حرارية**: تصور حركة اللاعبين والفريق
- **تتبع الأحداث**: تسجيل تلقائي لأحداث المباراة

## 🏗️ بنية المشروع

```
taktik/
├── backend/                    # خادم Python
│   ├── ai_models/             # نماذج الذكاء الاصطناعي
│   │   ├── detection/         # كشف اللاعبين والكرة
│   │   ├── tracking/          # تتبع الكيانات
│   │   ├── pose_estimation/   # تحليل الحركات
│   │   └── event_detection/   # تصنيف الأحداث
│   ├── data_processing/       # معالجة البيانات
│   ├── analytics/             # حساب الإحصائيات
│   ├── database/              # قاعدة البيانات
│   └── api/                   # واجهة برمجة التطبيقات
├── frontend/                  # تطبيق Flutter
│   ├── lib/
│   │   ├── models/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
├── data/                      # بيانات التدريب والاختبار
├── models/                    # النماذج المدربة
├── output/                    # مخرجات التحليل
└── docs/                      # التوثيق
```

## 🚀 البدء السريع

### المتطلبات الأساسية
- **Python 3.8+** (مطلوب)
- **FFmpeg** (مطلوب لمعالجة الفيديو)
- **CUDA** (اختياري للمعالجة بـ GPU)
- **Docker** (اختياري للنشر)

### التثبيت السريع

#### الطريقة الأولى: استخدام سكريبت التشغيل السريع

```bash
# تحميل المشروع
git clone https://github.com/your-username/taktik.git
cd taktik

# إعداد النظام بالكامل
python run.py setup

# تشغيل الخادم
python run.py server
```

#### الطريقة الثانية: التثبيت اليدوي

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/taktik.git
cd taktik
```

2. **إعداد البيئة الافتراضية**
```bash
python -m venv venv

# Linux/Mac
source venv/bin/activate

# Windows
venv\Scripts\activate
```

3. **تثبيت التبعيات**
```bash
pip install -r requirements.txt
```

4. **تهيئة قاعدة البيانات**
```bash
python backend/main.py init --sample
```

5. **تشغيل الخادم**
```bash
python backend/main.py server
```

#### الطريقة الثالثة: استخدام Docker

```bash
# بناء وتشغيل جميع الخدمات
docker-compose up -d

# أو استخدام سكريبت التشغيل
python run.py docker-run
```

### الوصول للنظام

بعد التشغيل، ستكون الخدمات متاحة على:

- **🌐 التطبيق الرئيسي**: http://localhost:8000
- **📚 توثيق API**: http://localhost:8000/docs
- **📊 لوحة Grafana**: http://localhost:3000 (admin/admin123)
- **🌸 مراقب المهام Flower**: http://localhost:5555
- **📈 Prometheus**: http://localhost:9090

### اختبار النظام

```bash
# تشغيل جميع الاختبارات
python run.py test

# أو يدوياً
pytest tests/

# معالجة فيديو تجريبي
python run.py process path/to/video.mp4
```

## 📊 النماذج المستخدمة

- **YOLOv8**: كشف اللاعبين والكرة
- **DeepSORT**: تتبع الكيانات
- **MediaPipe**: تحليل الحركات
- **SoccerNet**: تصنيف الأحداث
- **Custom Models**: تحليل التكتيك والأداء

## 🎥 المدخلات والمخرجات

### المدخلات
- فيديو مباراة (MP4, MKV)
- جودة 720p أو أعلى
- مدة: مباراة كاملة أو مقاطع

### المخرجات
- فيديو مُحلل مع التعليقات
- إحصائيات تفصيلية (JSON/CSV)
- خرائط حرارية
- تقرير PDF شامل
- لوحة تحكم تفاعلية

## 🔧 استخدام API

### رفع فيديو وتحليله

```bash
# إنشاء مباراة جديدة
curl -X POST "http://localhost:8000/matches/" \
  -H "Content-Type: application/json" \
  -d '{"name": "مباراة تجريبية", "date": "2024-01-15T20:00:00"}'

# رفع فيديو المباراة
curl -X POST "http://localhost:8000/matches/{match_id}/upload-video" \
  -F "file=@path/to/video.mp4"

# متابعة حالة المعالجة
curl "http://localhost:8000/matches/{match_id}"

# الحصول على الإحصائيات
curl "http://localhost:8000/matches/{match_id}/statistics"
```

### استخدام Python SDK

```python
import requests

# إنشاء مباراة
response = requests.post("http://localhost:8000/matches/",
                        json={"name": "مباراة تجريبية"})
match_id = response.json()["id"]

# رفع فيديو
with open("video.mp4", "rb") as f:
    files = {"file": f}
    response = requests.post(f"http://localhost:8000/matches/{match_id}/upload-video",
                           files=files)

# الحصول على النتائج
stats = requests.get(f"http://localhost:8000/matches/{match_id}/statistics")
print(stats.json())
```

## 🔧 التطوير

### إضافة نموذج جديد
1. إنشاء مجلد في `backend/ai_models/`
2. تنفيذ واجهة `BaseModel`
3. إضافة النموذج إلى pipeline

### تشغيل الاختبارات
```bash
# تشغيل جميع الاختبارات
python run.py test

# تشغيل اختبارات محددة
pytest tests/test_video_processor.py

# تشغيل مع تغطية الكود
pytest --cov=backend tests/
```

### إعداد بيئة التطوير

```bash
# تثبيت أدوات التطوير
pip install -e ".[dev]"

# تنسيق الكود
black backend/ tests/
isort backend/ tests/

# فحص جودة الكود
flake8 backend/ tests/
mypy backend/
```

## 📝 الترخيص

MIT License - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! راجع [CONTRIBUTING.md](CONTRIBUTING.md) للتفاصيل.

## 📞 التواصل

- البريد الإلكتروني: <EMAIL>
- GitHub Issues: [رفع مشكلة](https://github.com/your-username/taktik/issues)
