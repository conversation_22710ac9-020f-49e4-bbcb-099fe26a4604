# Core Dependencies
numpy>=1.21.0
pandas>=1.3.0
opencv-python>=4.5.0
pillow>=8.3.0
matplotlib>=3.4.0
seaborn>=0.11.0
plotly>=5.0.0
scikit-learn>=1.0.0
scipy>=1.7.0

# Deep Learning & Computer Vision
torch>=1.9.0
torchvision>=0.10.0
ultralytics>=8.0.0  # YOLOv8
mediapipe>=0.8.0
tensorflow>=2.6.0

# Object Tracking
filterpy>=1.4.5
lap>=0.4.0

# Video Processing
ffmpeg-python>=0.2.0
moviepy>=1.0.3

# OCR
easyocr>=1.6.0
pytesseract>=0.3.8

# Database
sqlalchemy>=1.4.0
psycopg2-binary>=2.9.0  # PostgreSQL
pymysql>=1.0.0  # MySQL

# Web Framework
fastapi>=0.68.0
uvicorn>=0.15.0
pydantic>=1.8.0
python-multipart>=0.0.5

# Data Processing
joblib>=1.0.0
tqdm>=4.62.0
h5py>=3.3.0

# Utilities
python-dotenv>=0.19.0
pyyaml>=5.4.0
requests>=2.26.0
aiofiles>=0.7.0

# Testing
pytest>=6.2.0
pytest-asyncio>=0.15.0
pytest-cov>=2.12.0

# Development
black>=21.7.0
flake8>=3.9.0
isort>=5.9.0

# Visualization
bokeh>=2.3.0
dash>=2.0.0

# Machine Learning Extensions
xgboost>=1.4.0
lightgbm>=3.2.0
catboost>=0.26.0

# Image Processing
albumentations>=1.0.0
imgaug>=0.4.0

# Geometric Calculations
shapely>=1.7.0
geopandas>=0.9.0

# Performance
numba>=0.54.0
cython>=0.29.0

# Logging
loguru>=0.5.0

# Configuration
hydra-core>=1.1.0
omegaconf>=2.1.0

# Async Processing
celery>=5.1.0
redis>=3.5.0

# File Handling
openpyxl>=3.0.0
xlsxwriter>=3.0.0

# API Documentation
fastapi-users>=10.0.0
python-jose>=3.3.0
passlib>=1.7.0

# Monitoring
prometheus-client>=0.11.0
psutil>=5.8.0
