"""
نماذج قاعدة البيانات لنظام تحليل مباريات كرة القدم
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()


class Match(Base):
    """جدول المباريات"""
    __tablename__ = "matches"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False)
    date = Column(DateTime, default=datetime.utcnow)
    duration = Column(Float)  # بالثواني

    # معلومات الفيديو
    video_path = Column(String(500))
    video_fps = Column(Float)
    video_width = Column(Integer)
    video_height = Column(Integer)

    # حالة المعالجة
    processing_status = Column(String(50), default="pending")  # pending, processing, completed, failed
    processing_progress = Column(Float, default=0.0)

    # إحصائيات عامة
    total_frames = Column(Integer)
    processed_frames = Column(Integer)
    total_events = Column(Integer)
    ball_in_play_time = Column(Float)

    # العلاقات
    teams = relationship("Team", back_populates="match")
    players = relationship("Player", back_populates="match")
    events = relationship("Event", back_populates="match")
    frame_data = relationship("FrameData", back_populates="match")

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class Team(Base):
    """جدول الفرق"""
    __tablename__ = "teams"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    match_id = Column(String(36), ForeignKey("matches.id"), nullable=False)
    team_number = Column(Integer, nullable=False)  # 1 أو 2
    name = Column(String(255))
    color = Column(String(7))  # كود اللون hex

    # إحصائيات الفريق
    possession_percentage = Column(Float, default=0.0)
    total_passes = Column(Integer, default=0)
    pass_accuracy = Column(Float, default=0.0)
    shots = Column(Integer, default=0)
    shots_on_target = Column(Integer, default=0)
    goals = Column(Integer, default=0)
    corners = Column(Integer, default=0)
    fouls = Column(Integer, default=0)
    yellow_cards = Column(Integer, default=0)
    red_cards = Column(Integer, default=0)

    # تكتيك
    formation = Column(String(20))

    # العلاقات
    match = relationship("Match", back_populates="teams")
    players = relationship("Player", back_populates="team")

    created_at = Column(DateTime, default=datetime.utcnow)


class Player(Base):
    """جدول اللاعبين"""
    __tablename__ = "players"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    match_id = Column(String(36), ForeignKey("matches.id"), nullable=False)
    team_id = Column(String(36), ForeignKey("teams.id"))

    # معلومات اللاعب
    track_id = Column(Integer, nullable=False)  # ID التتبع في الفيديو
    jersey_number = Column(Integer)
    name = Column(String(255))
    position = Column(String(50))  # goalkeeper, defender, midfielder, forward

    # إحصائيات الحركة
    total_distance = Column(Float, default=0.0)  # بالمتر
    max_speed = Column(Float, default=0.0)  # م/ث
    average_speed = Column(Float, default=0.0)  # م/ث
    sprint_count = Column(Integer, default=0)

    # إحصائيات الكرة
    ball_touches = Column(Integer, default=0)
    passes_attempted = Column(Integer, default=0)
    passes_completed = Column(Integer, default=0)
    pass_accuracy = Column(Float, default=0.0)
    shots = Column(Integer, default=0)
    shots_on_target = Column(Integer, default=0)
    goals = Column(Integer, default=0)
    assists = Column(Integer, default=0)

    # إحصائيات دفاعية
    tackles = Column(Integer, default=0)
    interceptions = Column(Integer, default=0)
    clearances = Column(Integer, default=0)
    blocks = Column(Integer, default=0)

    # إحصائيات هجومية
    dribbles = Column(Integer, default=0)
    crosses = Column(Integer, default=0)
    headers = Column(Integer, default=0)

    # إحصائيات انضباطية
    fouls_committed = Column(Integer, default=0)
    fouls_received = Column(Integer, default=0)
    yellow_cards = Column(Integer, default=0)
    red_cards = Column(Integer, default=0)

    # تقييمات الأداء
    technical_rating = Column(Float, default=0.0)
    physical_rating = Column(Float, default=0.0)
    tactical_rating = Column(Float, default=0.0)
    mental_rating = Column(Float, default=0.0)
    overall_rating = Column(Float, default=0.0)

    # خريطة حرارية (مخزنة كـ JSON)
    heatmap_data = Column(JSON)

    # العلاقات
    match = relationship("Match", back_populates="players")
    team = relationship("Team", back_populates="players")
    events = relationship("Event", back_populates="player")

    created_at = Column(DateTime, default=datetime.utcnow)


class Event(Base):
    """جدول أحداث المباراة"""
    __tablename__ = "events"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    match_id = Column(String(36), ForeignKey("matches.id"), nullable=False)
    player_id = Column(String(36), ForeignKey("players.id"))

    # معلومات الحدث
    event_type = Column(String(50), nullable=False)
    timestamp = Column(Float, nullable=False)  # بالثواني
    frame_id = Column(Integer, nullable=False)
    confidence = Column(Float, default=1.0)

    # موقع الحدث
    location_x = Column(Float)
    location_y = Column(Float)

    # معلومات إضافية
    successful = Column(Boolean, default=True)
    description = Column(Text)
    event_metadata = Column(JSON)  # بيانات إضافية

    # العلاقات
    match = relationship("Match", back_populates="events")
    player = relationship("Player", back_populates="events")

    created_at = Column(DateTime, default=datetime.utcnow)


class FrameData(Base):
    """جدول بيانات الإطارات"""
    __tablename__ = "frame_data"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    match_id = Column(String(36), ForeignKey("matches.id"), nullable=False)

    frame_id = Column(Integer, nullable=False)
    timestamp = Column(Float, nullable=False)

    # بيانات الكشف والتتبع
    detections = Column(JSON)  # قائمة الكشوفات
    tracks = Column(JSON)  # قائمة المسارات
    poses = Column(JSON)  # بيانات الوضعيات

    # معلومات الكرة
    ball_position_x = Column(Float)
    ball_position_y = Column(Float)
    ball_possession_team = Column(Integer)
    ball_possession_player = Column(Integer)

    # العلاقات
    match = relationship("Match", back_populates="frame_data")

    created_at = Column(DateTime, default=datetime.utcnow)


class Formation(Base):
    """جدول التشكيلات التكتيكية"""
    __tablename__ = "formations"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    match_id = Column(String(36), ForeignKey("matches.id"), nullable=False)
    team_id = Column(String(36), ForeignKey("teams.id"), nullable=False)

    # معلومات التشكيل
    formation_name = Column(String(20))  # مثل 4-3-3
    start_time = Column(Float)  # وقت البداية بالثواني
    end_time = Column(Float)  # وقت النهاية بالثواني

    # مواقع اللاعبين
    player_positions = Column(JSON)  # {player_id: {x: float, y: float, role: str}}

    # تقييم التشكيل
    effectiveness_score = Column(Float, default=0.0)

    created_at = Column(DateTime, default=datetime.utcnow)


class Analysis(Base):
    """جدول التحليلات المحفوظة"""
    __tablename__ = "analyses"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    match_id = Column(String(36), ForeignKey("matches.id"), nullable=False)

    # معلومات التحليل
    analysis_type = Column(String(50), nullable=False)  # tactical, performance, statistical
    title = Column(String(255), nullable=False)
    description = Column(Text)

    # نتائج التحليل
    results = Column(JSON)

    # ملفات مرفقة
    charts = Column(JSON)  # مسارات الرسوم البيانية
    reports = Column(JSON)  # مسارات التقارير

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class UserSession(Base):
    """جدول جلسات المستخدمين"""
    __tablename__ = "user_sessions"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    session_token = Column(String(255), unique=True, nullable=False)

    # معلومات الجلسة
    ip_address = Column(String(45))
    user_agent = Column(Text)

    # إعدادات المستخدم
    preferences = Column(JSON)

    # تتبع النشاط
    last_activity = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime)


class ProcessingJob(Base):
    """جدول مهام المعالجة"""
    __tablename__ = "processing_jobs"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    match_id = Column(String(36), ForeignKey("matches.id"))

    # معلومات المهمة
    job_type = Column(String(50), nullable=False)  # video_analysis, statistics_calculation
    status = Column(String(20), default="pending")  # pending, running, completed, failed
    progress = Column(Float, default=0.0)

    # معلومات الخطأ
    error_message = Column(Text)
    error_traceback = Column(Text)

    # أوقات التنفيذ
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)

    # معلومات إضافية
    parameters = Column(JSON)
    results = Column(JSON)


# فهارس لتحسين الأداء
from sqlalchemy import Index

# فهارس للبحث السريع
Index('idx_match_date', Match.date)
Index('idx_match_status', Match.processing_status)
Index('idx_event_match_timestamp', Event.match_id, Event.timestamp)
Index('idx_event_type', Event.event_type)
Index('idx_frame_data_match_frame', FrameData.match_id, FrameData.frame_id)
Index('idx_player_match_team', Player.match_id, Player.team_id)
Index('idx_processing_job_status', ProcessingJob.status)
