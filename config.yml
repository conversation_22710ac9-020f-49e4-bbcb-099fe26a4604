# تكوين نظام Taktik لتحليل مباريات كرة القدم

# إعدادات عامة
app:
  name: "Taktik Football Analysis System"
  version: "1.0.0"
  debug: true
  environment: "development"  # development, production, testing

# إعدادات قاعدة البيانات
database:
  # SQLite للتطوير
  url: "sqlite:///taktik.db"
  
  # PostgreSQL للإنتاج
  # url: "postgresql://username:password@localhost:5432/taktik_db"
  
  # إعدادات الاتصال
  pool_size: 10
  max_overflow: 20
  pool_timeout: 30
  pool_recycle: 3600
  echo: false  # تعيين true لرؤية استعلامات SQL

# إعدادات Redis (للتخزين المؤقت والمهام)
redis:
  url: "redis://localhost:6379/0"
  password: null
  db: 0
  max_connections: 10

# إعدادات الخادم
server:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  reload: true
  log_level: "info"
  access_log: true

# إعدادات الملفات
files:
  upload_dir: "uploads"
  output_dir: "output"
  static_dir: "static"
  models_dir: "models"
  data_dir: "data"
  logs_dir: "logs"
  
  # حد أقصى لحجم الملف (بالميجابايت)
  max_upload_size: 500
  
  # أنواع الملفات المسموحة
  allowed_video_formats: ["mp4", "avi", "mov", "mkv", "wmv", "flv", "webm"]
  allowed_image_formats: ["jpg", "jpeg", "png", "bmp", "tiff"]

# إعدادات معالجة الفيديو
video_processing:
  # إعدادات عامة
  frame_skip: 1  # معالجة كل إطار
  max_frames: null  # معالجة جميع الإطارات
  save_annotated_video: true
  save_raw_data: true
  save_statistics: true
  
  # إعدادات الجودة
  output_quality: "high"  # low, medium, high
  compression_level: 23  # 0-51 (أقل = جودة أعلى)
  
  # إعدادات المعالجة المتوازية
  use_multiprocessing: true
  num_workers: 4

# إعدادات نماذج الذكاء الاصطناعي
ai_models:
  # إعدادات عامة
  device: "auto"  # auto, cpu, cuda, mps
  precision: "fp32"  # fp16, fp32
  
  # نموذج كشف الكيانات (YOLO)
  detection:
    model_name: "yolov8n.pt"  # yolov8n, yolov8s, yolov8m, yolov8l, yolov8x
    confidence_threshold: 0.5
    iou_threshold: 0.45
    max_detections: 100
    input_size: 640
    
  # نموذج التتبع (DeepSORT)
  tracking:
    max_age: 30
    min_hits: 3
    iou_threshold: 0.3
    feature_threshold: 0.6
    input_size: [64, 128]  # [width, height]
    
  # نموذج تحليل الوضعيات (MediaPipe)
  pose_estimation:
    model_complexity: 1  # 0, 1, 2
    min_detection_confidence: 0.5
    min_tracking_confidence: 0.5
    smooth_landmarks: true
    enable_segmentation: false
    
  # نموذج تصنيف الأحداث
  event_detection:
    window_size: 30
    confidence_threshold: 0.6
    model_type: "random_forest"  # random_forest, neural_network
    feature_scaler: true

# إعدادات التحليل
analysis:
  # أبعاد الملعب (بالمتر)
  field_dimensions:
    length: 105.0
    width: 68.0
  
  # إعدادات الخرائط الحرارية
  heatmap:
    size: [50, 80]  # [height, width]
    gaussian_sigma: 1.0
    
  # إعدادات تحليل الأداء
  performance:
    sprint_speed_threshold: 5.0  # م/ث
    high_intensity_threshold: 4.0  # م/ث
    possession_radius: 2.0  # متر
    
  # إعدادات التشكيلات التكتيكية
  formations:
    detection_window: 60  # ثانية
    stability_threshold: 0.8
    common_formations: ["4-4-2", "4-3-3", "3-5-2", "4-2-3-1", "5-3-2"]

# إعدادات التصدير
export:
  formats: ["json", "csv", "pdf", "xlsx"]
  
  # إعدادات PDF
  pdf:
    page_size: "A4"
    orientation: "portrait"
    include_charts: true
    include_heatmaps: true
    
  # إعدادات Excel
  excel:
    include_charts: true
    separate_sheets: true

# إعدادات الأمان
security:
  # مفتاح التشفير (يجب تغييره في الإنتاج)
  secret_key: "your-secret-key-change-in-production"
  
  # إعدادات CORS
  cors:
    allow_origins: ["*"]
    allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allow_headers: ["*"]
    allow_credentials: true
    
  # إعدادات التحميل
  upload:
    scan_files: true  # فحص الملفات للفيروسات
    max_file_size: 524288000  # 500MB بالبايت
    
  # إعدادات المصادقة
  authentication:
    enabled: false  # تعطيل المصادقة للتطوير
    token_expiry: 3600  # ثانية
    refresh_token_expiry: 604800  # أسبوع

# إعدادات التسجيل
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # ملفات التسجيل
  files:
    app: "logs/app.log"
    error: "logs/error.log"
    access: "logs/access.log"
    
  # إعدادات التدوير
  rotation:
    max_size: "10MB"
    backup_count: 5
    
  # تسجيل قاعدة البيانات
  database_logging: false

# إعدادات المراقبة
monitoring:
  # Prometheus
  prometheus:
    enabled: true
    port: 9090
    
  # Grafana
  grafana:
    enabled: true
    port: 3000
    admin_password: "admin123"
    
  # إحصائيات النظام
  system_stats:
    enabled: true
    interval: 60  # ثانية
    
  # تنبيهات
  alerts:
    enabled: false
    email_notifications: false
    webhook_url: null

# إعدادات التطوير
development:
  # إعادة التحميل التلقائي
  auto_reload: true
  
  # بيانات تجريبية
  sample_data: true
  
  # تفعيل وضع التصحيح
  debug_mode: true
  
  # عرض الأخطاء التفصيلية
  show_error_details: true

# إعدادات الإنتاج
production:
  # تحسينات الأداء
  optimize_models: true
  use_compiled_models: true
  
  # إعدادات الأمان
  secure_headers: true
  rate_limiting: true
  
  # إعدادات التخزين المؤقت
  caching:
    enabled: true
    ttl: 3600  # ثانية
    
  # إعدادات النسخ الاحتياطي
  backup:
    enabled: true
    interval: "daily"
    retention_days: 30

# إعدادات الاختبار
testing:
  database_url: "sqlite:///:memory:"
  use_mock_models: true
  sample_video_duration: 10  # ثانية
  skip_slow_tests: true
